import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Modal,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { X, ZoomIn } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ImageCarouselProps {
  images: string[];
  featuredImageIndex?: number;
  productName?: string;
  onImagePress?: (imageUrl: string, index: number) => void;
}

export default function ImageCarousel({
  images,
  featuredImageIndex = 0,
  productName = 'Product',
  onImagePress,
}: ImageCarouselProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [currentImageIndex, setCurrentImageIndex] = useState(featuredImageIndex);
  const [imageLoading, setImageLoading] = useState<Record<string, boolean>>({});
  const [imageError, setImageError] = useState<Record<string, boolean>>({});
  const [isZoomModalOpen, setIsZoomModalOpen] = useState(false);
  
  const mainScrollRef = useRef<ScrollView>(null);
  const thumbnailScrollRef = useRef<FlatList>(null);

  // Colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const cardBackgroundColor = isDark ? '#1F1F1F' : '#F8F9FA';
  const borderColor = isDark ? '#333333' : '#E5E7EB';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#9CA3AF' : '#6B7280';

  // Ensure we have valid images array
  const validImages = images && images.length > 0 ? images : [];
  const hasMultipleImages = validImages.length > 1;

  useEffect(() => {
    // Scroll to featured image on mount
    if (mainScrollRef.current && hasMultipleImages) {
      setTimeout(() => {
        mainScrollRef.current?.scrollTo({
          x: currentImageIndex * screenWidth,
          animated: false,
        });
      }, 100);
    }
  }, []);

  const handleMainImageScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const imageIndex = Math.round(contentOffset.x / screenWidth);
    
    if (imageIndex !== currentImageIndex && imageIndex >= 0 && imageIndex < validImages.length) {
      setCurrentImageIndex(imageIndex);
      
      // Scroll thumbnail to center the selected image
      if (thumbnailScrollRef.current) {
        const thumbnailWidth = 80;
        const thumbnailSpacing = 8;
        const totalThumbnailWidth = thumbnailWidth + thumbnailSpacing;
        const scrollToX = Math.max(0, imageIndex * totalThumbnailWidth - (screenWidth / 2) + (thumbnailWidth / 2));
        
        thumbnailScrollRef.current.scrollToOffset({
          offset: scrollToX,
          animated: true,
        });
      }
    }
  };

  const handleThumbnailPress = (index: number) => {
    setCurrentImageIndex(index);
    mainScrollRef.current?.scrollTo({
      x: index * screenWidth,
      animated: true,
    });
  };

  const handleImagePress = (imageUrl: string, index: number) => {
    if (onImagePress) {
      onImagePress(imageUrl, index);
    } else {
      setIsZoomModalOpen(true);
    }
  };

  const handleImageLoad = (imageUrl: string) => {
    setImageLoading(prev => ({ ...prev, [imageUrl]: false }));
  };

  const handleImageError = (imageUrl: string) => {
    setImageError(prev => ({ ...prev, [imageUrl]: true }));
    setImageLoading(prev => ({ ...prev, [imageUrl]: false }));
  };

  if (validImages.length === 0) {
    return (
      <View style={{
        width: screenWidth,
        height: screenWidth * 0.75,
        backgroundColor: cardBackgroundColor,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <Text style={{ color: subtitleColor, fontSize: 16 }}>No images available</Text>
      </View>
    );
  }

  const renderMainImage = ({ item: imageUrl, index }: { item: string; index: number }) => (
    <TouchableOpacity
      style={{
        width: screenWidth,
        height: screenWidth * 0.75,
        position: 'relative',
      }}
      onPress={() => handleImagePress(imageUrl, index)}
      activeOpacity={0.9}
    >
      {!imageError[imageUrl] ? (
        <Image
          source={{ uri: imageUrl }}
          style={{
            width: '100%',
            height: '100%',
            resizeMode: 'cover',
          }}
          onLoadStart={() => setImageLoading(prev => ({ ...prev, [imageUrl]: true }))}
          onLoad={() => handleImageLoad(imageUrl)}
          onError={() => handleImageError(imageUrl)}
        />
      ) : (
        <View style={{
          width: '100%',
          height: '100%',
          backgroundColor: cardBackgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{ color: subtitleColor }}>Image not available</Text>
        </View>
      )}
      
      {imageLoading[imageUrl] && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
        }}>
          <ActivityIndicator size="large" color="#D4AF37" />
        </View>
      )}

      {/* Zoom indicator */}
      <View style={{
        position: 'absolute',
        top: 12,
        right: 12,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        borderRadius: 20,
        padding: 8,
      }}>
        <ZoomIn size={16} color="#FFFFFF" />
      </View>
    </TouchableOpacity>
  );

  const renderThumbnail = ({ item: imageUrl, index }: { item: string; index: number }) => (
    <TouchableOpacity
      style={{
        width: 80,
        height: 80,
        marginRight: 8,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: index === currentImageIndex ? '#D4AF37' : 'transparent',
        overflow: 'hidden',
      }}
      onPress={() => handleThumbnailPress(index)}
      activeOpacity={0.7}
    >
      {!imageError[imageUrl] ? (
        <Image
          source={{ uri: imageUrl }}
          style={{
            width: '100%',
            height: '100%',
            resizeMode: 'cover',
          }}
          onLoad={() => handleImageLoad(imageUrl)}
          onError={() => handleImageError(imageUrl)}
        />
      ) : (
        <View style={{
          width: '100%',
          height: '100%',
          backgroundColor: cardBackgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{ color: subtitleColor, fontSize: 10 }}>N/A</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View>
      {/* Main Image Carousel */}
      <FlatList
        ref={mainScrollRef}
        data={validImages}
        renderItem={renderMainImage}
        keyExtractor={(item, index) => `main-${index}-${item}`}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleMainImageScroll}
        getItemLayout={(data, index) => ({
          length: screenWidth,
          offset: screenWidth * index,
          index,
        })}
      />

      {/* Image Counter */}
      {hasMultipleImages && (
        <View style={{
          position: 'absolute',
          bottom: 12,
          left: 12,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          borderRadius: 16,
          paddingHorizontal: 12,
          paddingVertical: 6,
        }}>
          <Text style={{ color: '#FFFFFF', fontSize: 12, fontWeight: '500' }}>
            {currentImageIndex + 1} / {validImages.length}
          </Text>
        </View>
      )}

      {/* Thumbnail Carousel */}
      {hasMultipleImages && (
        <View style={{ marginTop: 12, paddingHorizontal: 16 }}>
          <FlatList
            ref={thumbnailScrollRef}
            data={validImages}
            renderItem={renderThumbnail}
            keyExtractor={(item, index) => `thumb-${index}-${item}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 8 }}
          />
        </View>
      )}

      {/* Zoom Modal */}
      <Modal
        visible={isZoomModalOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsZoomModalOpen(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.9)',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 50,
              right: 20,
              zIndex: 1,
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 20,
              padding: 8,
            }}
            onPress={() => setIsZoomModalOpen(false)}
          >
            <X size={24} color="#FFFFFF" />
          </TouchableOpacity>
          
          {validImages[currentImageIndex] && (
            <Image
              source={{ uri: validImages[currentImageIndex] }}
              style={{
                width: screenWidth - 40,
                height: screenHeight - 200,
                resizeMode: 'contain',
              }}
            />
          )}
        </View>
      </Modal>
    </View>
  );
}
