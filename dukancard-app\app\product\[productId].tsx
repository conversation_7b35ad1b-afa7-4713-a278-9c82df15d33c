import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  Heart,
  Share2,
  Phone,
  MessageCircle,
  User,
  ShoppingBag,
  Star,
  MapPin,
} from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { BusinessProduct } from '@/lib/services/businessCardDataService';
import { logError } from '@/lib/utils/errorHandling';
import { Toast } from '@/lib/utils/toast';
import { formatIndianNumberShort } from '@/lib/utils';
import { createSingleProductStyles } from '@/styles/product/single-product-styles';
import ImageCarousel from '@/components/product/ImageCarousel';
import VariantSelector from '@/components/product/VariantSelector';
import ProductRecommendations from '@/components/product/ProductRecommendations';

// Product variant interface
interface ProductVariant {
  id: string;
  variant_name: string;
  variant_values: Record<string, string>;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available: boolean;
  images: string[];
  featured_image_index: number;
}

// Temporary type for product data
interface Product {
  id: string;
  name: string;
  description?: string;
  base_price: number;
  discounted_price?: number;
  image_url?: string;
  images?: string[];
  featured_image_index?: number;
  business_id: string;
  product_variants?: ProductVariant[];
}

// Business profile interface for this component
interface BusinessProfile {
  id: string;
  business_name?: string | null;
  business_slug?: string | null;
  phone?: string | null;
  whatsapp_number?: string | null;
  business_category?: string | null;
  member_name?: string | null;
  title?: string | null;
  about_bio?: string | null;
  address_line?: string | null;
  city?: string | null;
  state?: string | null;
  pincode?: string | null;
  locality?: string | null;
}

interface ProductPageData {
  product: Product;
  business: BusinessProfile;
}

// Temporary function to format currency
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Function to get product by ID using existing service
const getProductById = async (productId: string): Promise<{ success: boolean; data?: Product; message?: string }> => {
  try {
    // Import the supabase admin client
    const { supabaseAdmin } = await import('@/lib/utils/supabaseAdmin');

    const { data, error } = await supabaseAdmin
      .from('products_services')
      .select(`
        id,
        name,
        description,
        base_price,
        discounted_price,
        image_url,
        images,
        featured_image_index,
        business_id,
        is_available,
        product_variants (
          id,
          variant_name,
          variant_values,
          base_price,
          discounted_price,
          is_available,
          images,
          featured_image_index
        )
      `)
      .eq('id', productId)
      .eq('is_available', true)
      .single();

    if (error) {
      console.error('Error fetching product:', error);
      return {
        success: false,
        message: 'Product not found'
      };
    }

    return {
      success: true,
      data: data as Product
    };
  } catch (error) {
    console.error('Error in getProductById:', error);
    return {
      success: false,
      message: 'Failed to fetch product'
    };
  }
};

// Function to get business profile by ID
const getBusinessProfileById = async (businessId: string): Promise<{ success: boolean; data?: BusinessProfile; message?: string }> => {
  try {
    // Import the supabase admin client
    const { supabaseAdmin } = await import('@/lib/utils/supabaseAdmin');

    const { data, error } = await supabaseAdmin
      .from('business_profiles')
      .select(`
        id,
        business_name,
        business_slug,
        phone,
        whatsapp_number,
        business_category,
        member_name,
        title,
        about_bio,
        address_line,
        city,
        state,
        pincode,
        locality
      `)
      .eq('id', businessId)
      .single();

    if (error) {
      console.error('Error fetching business profile:', error);
      return {
        success: false,
        message: 'Business profile not found'
      };
    }

    return {
      success: true,
      data: data as BusinessProfile
    };
  } catch (error) {
    console.error('Error in getBusinessProfileById:', error);
    return {
      success: false,
      message: 'Failed to fetch business profile'
    };
  }
};

// Function to get more products from the same business
const getMoreProductsFromBusiness = async (
  businessId: string,
  currentProductId: string,
  limit: number = 8
): Promise<{ success: boolean; data?: Product[]; message?: string }> => {
  try {
    const { supabaseAdmin } = await import('@/lib/utils/supabaseAdmin');

    const { data, error } = await supabaseAdmin
      .from('products_services')
      .select(`
        id,
        name,
        description,
        base_price,
        discounted_price,
        image_url,
        images,
        featured_image_index,
        business_id,
        is_available
      `)
      .eq('business_id', businessId)
      .eq('is_available', true)
      .neq('id', currentProductId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching more products:', error);
      return {
        success: false,
        message: 'Failed to fetch more products'
      };
    }

    return {
      success: true,
      data: data as Product[]
    };
  } catch (error) {
    console.error('Error in getMoreProductsFromBusiness:', error);
    return {
      success: false,
      message: 'Failed to fetch more products'
    };
  }
};

// Function to get products from other businesses
const getProductsFromOtherBusinesses = async (
  businessId: string,
  limit: number = 8
): Promise<{ success: boolean; data?: (Product & { business_slug: string })[]; message?: string }> => {
  try {
    const { supabaseAdmin } = await import('@/lib/utils/supabaseAdmin');

    // Get all online business IDs
    const { data: validBusinesses, error: businessError } = await supabaseAdmin
      .from('business_profiles')
      .select('id')
      .neq('id', businessId)
      .eq('status', 'online');

    if (businessError || !validBusinesses || validBusinesses.length === 0) {
      return { success: true, data: [] };
    }

    const validBusinessIds = validBusinesses.map(b => b.id);

    // Fetch products from valid businesses
    const { data, error } = await supabaseAdmin
      .from('products_services')
      .select(`
        id,
        name,
        description,
        base_price,
        discounted_price,
        image_url,
        images,
        featured_image_index,
        business_id,
        is_available,
        business_profiles!business_id(business_slug)
      `)
      .in('business_id', validBusinessIds)
      .eq('is_available', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching products from other businesses:', error);
      return {
        success: false,
        message: 'Failed to fetch products from other businesses'
      };
    }

    // Transform the data to include business_slug
    const transformedData = data.map(item => {
      let businessSlug = null;
      if (item.business_profiles) {
        if (Array.isArray(item.business_profiles)) {
          businessSlug = item.business_profiles[0]?.business_slug || null;
        } else {
          businessSlug = (item.business_profiles as any).business_slug || null;
        }
      }

      const { business_profiles, ...productData } = item;
      return {
        ...productData,
        business_slug: businessSlug
      } as Product & { business_slug: string };
    });

    return {
      success: true,
      data: transformedData
    };
  } catch (error) {
    console.error('Error in getProductsFromOtherBusinesses:', error);
    return {
      success: false,
      message: 'Failed to fetch products from other businesses'
    };
  }
};

export default function SingleProductPage() {
  const { productId } = useLocalSearchParams<{ productId: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Styles
  const styles = createSingleProductStyles();

  // State management
  const [data, setData] = useState<ProductPageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [businessProducts, setBusinessProducts] = useState<Product[]>([]);
  const [otherBusinessProducts, setOtherBusinessProducts] = useState<(Product & { business_slug: string })[]>([]);
  const [recommendationsLoading, setRecommendationsLoading] = useState(false);

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#374151' : '#E5E7EB';
  const cardBackgroundColor = isDark ? '#1F2937' : '#F9FAFB';

  // Fetch product data
  useEffect(() => {
    // Validate productId parameter
    if (!productId || typeof productId !== 'string') {
      router.replace('/');
      return;
    }
    const fetchProductData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch product details
        const productResult = await getProductById(productId);
        if (!productResult.success || !productResult.data) {
          throw new Error(productResult.message || 'Failed to fetch product');
        }

        const product = productResult.data;

        // Fetch business profile
        const businessResult = await getBusinessProfileById(product.business_id);
        if (!businessResult.success || !businessResult.data) {
          throw new Error(businessResult.message || 'Failed to fetch business profile');
        }

        const business = businessResult.data;

        setData({ product, business });
        setCurrentProduct(product);

        // Fetch recommendations in the background
        setRecommendationsLoading(true);
        try {
          const [businessProductsResult, otherProductsResult] = await Promise.all([
            getMoreProductsFromBusiness(product.business_id, product.id),
            getProductsFromOtherBusinesses(product.business_id)
          ]);

          if (businessProductsResult.success && businessProductsResult.data) {
            setBusinessProducts(businessProductsResult.data);
          }

          if (otherProductsResult.success && otherProductsResult.data) {
            setOtherBusinessProducts(otherProductsResult.data);
          }
        } catch (err) {
          console.error('Error fetching recommendations:', err);
        } finally {
          setRecommendationsLoading(false);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
        logError('SingleProductPage', errorMessage);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [productId, router]);

  // Early return for invalid productId (after hooks)
  if (!productId || typeof productId !== 'string') {
    return null;
  }

  // Navigation handlers
  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace('/');
    }
  };

  const handleBusinessPress = () => {
    if (data?.business.business_slug) {
      router.push(`/business/${data.business.business_slug}`);
    }
  };

  const handleShare = async () => {
    try {
      // TODO: Implement sharing functionality
      Toast.show('Share functionality coming soon!', 'info');
    } catch (err) {
      logError('SingleProductPage', 'Share failed');
      Toast.show('Failed to share product', 'error');
    }
  };

  const handleWhatsApp = () => {
    if (data?.business.whatsapp_number && currentProduct) {
      // Format WhatsApp number (remove any non-digit characters)
      const formattedNumber = data.business.whatsapp_number.replace(/\D/g, '');

      // Create detailed message with product information
      const message = `Hi ${data.business.business_name || 'there'}, I'm interested in your product "${currentProduct.name}" that I saw on your Dukancard. Can you provide more information?`;

      const url = `whatsapp://send?phone=${formattedNumber}&text=${encodeURIComponent(message)}`;
      Linking.openURL(url).catch(() => {
        Toast.show('WhatsApp not installed', 'error');
      });
    }
  };

  const handleCall = () => {
    if (data?.business.phone) {
      const url = `tel:${data.business.phone}`;
      Linking.openURL(url).catch(() => {
        Toast.show('Unable to make call', 'error');
      });
    }
  };

  // Handle variant selection and deselection
  const handleVariantSelect = (variant: ProductVariant | null) => {
    setSelectedVariant(variant);

    if (variant && data?.product) {
      // Update current product data with variant-specific information
      const updatedProduct = {
        ...data.product,
        base_price: variant.base_price || data.product.base_price,
        discounted_price: variant.discounted_price || data.product.discounted_price,
        images: variant.images && variant.images.length > 0 ? variant.images : data.product.images,
        featured_image_index: variant.images && variant.images.length > 0 ? variant.featured_image_index : data.product.featured_image_index,
      };

      setCurrentProduct(updatedProduct);
    } else if (data?.product) {
      // Revert to base product when no variant is selected
      setCurrentProduct(data.product);
    }
  };

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <StatusBar style={isDark ? 'light' : 'dark'} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D4AF37" testID="loading-indicator" />
          <Text style={[styles.loadingText, { color: textColor }]}>Loading product...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error || !data) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <StatusBar style={isDark ? 'light' : 'dark'} />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: textColor }]}>Product Not Found</Text>
          <Text style={[styles.errorMessage, { color: subtitleColor }]}>
            {error || 'The product you are looking for could not be found.'}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleBack}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const { product, business } = data;
  const displayProduct = currentProduct || product;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Header */}
      <View style={[styles.header, { borderBottomColor: borderColor }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack} testID="back-button">
          <ArrowLeft size={24} color={textColor} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: textColor }]}>Product Details</Text>
        <TouchableOpacity style={styles.shareButton} onPress={handleShare} testID="share-button">
          <Share2 size={24} color={textColor} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Image Carousel */}
        <ImageCarousel
          images={displayProduct.images && displayProduct.images.length > 0 ? displayProduct.images : (displayProduct.image_url ? [displayProduct.image_url] : [])}
          featuredImageIndex={displayProduct.featured_image_index || 0}
          productName={displayProduct.name}
        />

        {/* Product Info */}
        <View style={[styles.productInfo, { backgroundColor: cardBackgroundColor }]}>
          <Text style={[styles.productName, { color: textColor }]}>{displayProduct.name}</Text>

          {/* Price */}
          <View style={styles.priceContainer}>
            {displayProduct.discounted_price && displayProduct.discounted_price < displayProduct.base_price ? (
              <>
                <Text style={[styles.discountedPrice, { color: '#D4AF37' }]}>
                  {formatCurrency(displayProduct.discounted_price)}
                </Text>
                <Text style={[styles.originalPrice, { color: subtitleColor }]}>
                  {formatCurrency(displayProduct.base_price)}
                </Text>
              </>
            ) : (
              <Text style={[styles.price, { color: '#D4AF37' }]}>
                {formatCurrency(displayProduct.base_price)}
              </Text>
            )}
          </View>

          {/* Description */}
          {displayProduct.description && (
            <Text style={[styles.description, { color: subtitleColor }]}>
              {displayProduct.description}
            </Text>
          )}
        </View>

        {/* Variant Selector */}
        {product.product_variants && product.product_variants.length > 0 && (
          <VariantSelector
            variants={product.product_variants}
            selectedVariant={selectedVariant}
            onVariantSelect={handleVariantSelect}
          />
        )}

        {/* Business Info */}
        <TouchableOpacity
          style={[styles.businessInfo, { backgroundColor: cardBackgroundColor }]}
          onPress={handleBusinessPress}
          activeOpacity={0.7}
          testID="business-profile"
        >
          <View style={styles.businessHeader}>
            <View style={styles.businessAvatar}>
              {business.business_name ? (
                <User size={24} color={subtitleColor} />
              ) : (
                <User size={24} color={subtitleColor} />
              )}
            </View>
            <View style={styles.businessDetails}>
              <Text style={[styles.businessName, { color: textColor }]}>{business.business_name || 'Business'}</Text>
              {business.business_slug && (
                <Text style={[styles.businessSlug, { color: subtitleColor }]}>
                  @{business.business_slug}
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>

        {/* Contact Buttons */}
        <View style={styles.contactButtons}>
          {business.whatsapp_number && (
            <TouchableOpacity style={styles.whatsappButton} onPress={handleWhatsApp} testID="whatsapp-button">
              <MessageCircle size={20} color="#FFFFFF" />
              <Text style={styles.contactButtonText}>WhatsApp</Text>
            </TouchableOpacity>
          )}
          {business.phone && (
            <TouchableOpacity style={styles.callButton} onPress={handleCall} testID="phone-button">
              <Phone size={20} color="#FFFFFF" />
              <Text style={styles.contactButtonText}>Call</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Product Recommendations */}
        <ProductRecommendations
          businessProducts={businessProducts}
          otherBusinessProducts={otherBusinessProducts}
          businessSlug={business.business_slug || ''}
          loading={recommendationsLoading}
        />
      </ScrollView>
    </SafeAreaView>
  );
}
