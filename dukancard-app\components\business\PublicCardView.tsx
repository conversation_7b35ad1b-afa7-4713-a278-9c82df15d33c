import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  Linking,
  Dimensions,
  FlatList,
  Animated,
} from 'react-native';
import {
  MapPin,
  Phone,
  Instagram,
  Facebook,
  MessageCircle,
  ExternalLink,
  Clock,
  Star,
  Heart,
  Users,
  Calendar,
  Package,
  ImageIcon,
  MessageSquare,
  ArrowLeft,
  Loader2,
} from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { Toast } from '../../lib/utils/toast';
import { formatIndianNumberShort } from '@/lib/utils';
import { useRouter } from 'expo-router';
import {
  fetchBusinessCardData,
  BusinessCardData,
  BusinessProduct,
  BusinessReview,
  BusinessGalleryImage
} from '../../lib/services/businessCardDataService';
import { LoadingSpinner } from '../shared/ui/LoadingSpinner';
import { ProductCard } from '../shared/ui';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';
import {
  getBusinessInteractionStatus,
  toggleBusinessLike,
  toggleBusinessSubscription,
  isBusinessOwner,
  BusinessInteractionStatus
} from '../../lib/services/businessInteractions';
import ReviewModal from './ReviewModal';
import FullScreenImageViewer from './FullScreenImageViewer';
import {
  fetchBusinessProductsPaginated,
  fetchBusinessReviewsPaginated
} from '../../lib/services/businessCardDataService';
import { supabase } from '../../lib/supabase';

interface PublicCardViewProps {
  businessData: BusinessDiscoveryData;
  onClose?: () => void;
}

const { width } = Dimensions.get('window');

export default function PublicCardView({ businessData, onClose }: PublicCardViewProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createPublicCardViewStyles(isDark);
  const router = useRouter();

  const [imageError, setImageError] = useState(false);
  const [cardData, setCardData] = useState<BusinessCardData | null>(null);
  const [loadingCardData, setLoadingCardData] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'about' | 'products' | 'gallery' | 'reviews'>('about');
  const [interactionStatus, setInteractionStatus] = useState<BusinessInteractionStatus | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const [interactionLoading, setInteractionLoading] = useState(false);
  const [likeLoading, setLikeLoading] = useState(false);
  const [subscribeLoading, setSubscribeLoading] = useState(false);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [fullScreenImageVisible, setFullScreenImageVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Pagination states
  const [allProducts, setAllProducts] = useState<BusinessProduct[]>([]);
  const [allReviews, setAllReviews] = useState<BusinessReview[]>([]);
  const [productsPage, setProductsPage] = useState(1);
  const [reviewsPage, setReviewsPage] = useState(1);
  const [loadingMoreProducts, setLoadingMoreProducts] = useState(false);
  const [loadingMoreReviews, setLoadingMoreReviews] = useState(false);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [hasMoreReviews, setHasMoreReviews] = useState(true);

  // Animation values for floating buttons
  const likeScale = new Animated.Value(1);
  const subscribeScale = new Animated.Value(1);
  const reviewScale = new Animated.Value(1);

  // Animation values for spinners
  const likeSpinValue = new Animated.Value(0);
  const subscribeSpinValue = new Animated.Value(0);

  // Pulse animation for active buttons
  useEffect(() => {
    if (interactionStatus?.isLiked) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(likeScale, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(likeScale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      likeScale.setValue(1);
    }
  }, [interactionStatus?.isLiked]);

  useEffect(() => {
    if (interactionStatus?.isSubscribed) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(subscribeScale, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(subscribeScale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      subscribeScale.setValue(1);
    }
  }, [interactionStatus?.isSubscribed]);

  useEffect(() => {
    if (interactionStatus?.userRating) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(reviewScale, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(reviewScale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      reviewScale.setValue(1);
    }
  }, [interactionStatus?.userRating]);

  // Spinner animations
  useEffect(() => {
    if (likeLoading) {
      Animated.loop(
        Animated.timing(likeSpinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();
    } else {
      likeSpinValue.setValue(0);
    }
  }, [likeLoading]);

  useEffect(() => {
    if (subscribeLoading) {
      Animated.loop(
        Animated.timing(subscribeSpinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();
    } else {
      subscribeSpinValue.setValue(0);
    }
  }, [subscribeLoading]);

  const handlePhonePress = () => {
    if (businessData.phone) {
      Linking.openURL(`tel:${businessData.phone}`);
    }
  };

  const handleWhatsAppPress = () => {
    if (businessData.whatsapp_number) {
      const message = `Hi ${businessData.business_name}, I found you through Dukancard!`;
      const url = `whatsapp://send?phone=${businessData.whatsapp_number}&text=${encodeURIComponent(message)}`;
      Linking.openURL(url).catch(() => {
        Toast.show('WhatsApp is not installed', 'error');
      });
    }
  };

  const handleInstagramPress = () => {
    if (businessData.instagram_url) {
      Linking.openURL(businessData.instagram_url).catch(() => {
        Toast.show('Unable to open Instagram', 'error');
      });
    }
  };

  const handleFacebookPress = () => {
    if (businessData.facebook_url) {
      Linking.openURL(businessData.facebook_url).catch(() => {
        Toast.show('Unable to open Facebook', 'error');
      });
    }
  };

  const handleMapsPress = () => {
    if (businessData.google_maps_url) {
      Linking.openURL(businessData.google_maps_url).catch(() => {
        Toast.show('Unable to open Maps', 'error');
      });
    }
  };

  const handleProductPress = (productId: string) => {
    router.push(`/product/${productId}`);
  };

  const formatAddress = () => {
    const parts = [
      businessData.address_line,
      businessData.locality,
      businessData.city,
      businessData.state,
      businessData.pincode,
    ].filter(Boolean);
    return parts.join(', ');
  };

  // Handle like button press
  const handleLikePress = async () => {
    if (isOwner) {
      Toast.show('You cannot like your own business', 'error');
      return;
    }

    if (likeLoading) return;

    setLikeLoading(true);
    try {
      const result = await toggleBusinessLike(businessData.id);

      if (result.success) {
        Toast.show(result.message, 'success');
        // Refresh interaction status
        const statusResult = await getBusinessInteractionStatus(businessData.id);
        if (statusResult.success && statusResult.data) {
          setInteractionStatus(statusResult.data);
        }
      } else {
        Toast.show(result.message, 'error');
      }
    } catch (error) {
      Toast.show('Failed to update like status', 'error');
    } finally {
      setLikeLoading(false);
    }
  };

  // Handle subscribe button press
  const handleSubscribePress = async () => {
    if (isOwner) {
      Toast.show('You cannot subscribe to your own business', 'error');
      return;
    }

    if (subscribeLoading) return;

    setSubscribeLoading(true);
    try {
      const result = await toggleBusinessSubscription(businessData.id);

      if (result.success) {
        Toast.show(result.message, 'success');
        // Refresh interaction status
        const statusResult = await getBusinessInteractionStatus(businessData.id);
        if (statusResult.success && statusResult.data) {
          setInteractionStatus(statusResult.data);
        }
      } else {
        Toast.show(result.message, 'error');
      }
    } catch (error) {
      Toast.show('Failed to update subscription status', 'error');
    } finally {
      setSubscribeLoading(false);
    }
  };

  // Handle review button press
  const handleReviewPress = () => {
    if (isOwner) {
      Toast.show('You cannot review your own business', 'error');
      return;
    }

    setReviewModalVisible(true);
  };

  // Handle review submitted
  const handleReviewSubmitted = async () => {
    // Refresh interaction status to get updated review data
    const statusResult = await getBusinessInteractionStatus(businessData.id);
    if (statusResult.success && statusResult.data) {
      setInteractionStatus(statusResult.data);
    }
  };

  // Load additional business card data
  useEffect(() => {
    const loadCardData = async () => {
      if (!businessData.id || !businessData.business_slug) return;

      try {
        setLoadingCardData(true);
        const result = await fetchBusinessCardData(businessData.id, businessData.business_slug);

        if (result.success && result.data) {
          setCardData(result.data);
        } else {
          console.warn('Failed to load card data:', result.error);
        }
      } catch (error) {
        console.error('Error loading card data:', error);
      } finally {
        setLoadingCardData(false);
      }
    };

    loadCardData();
  }, [businessData.id, businessData.business_slug]);

  // Load interaction status and check ownership
  useEffect(() => {
    const loadInteractionStatus = async () => {
      if (!businessData.id) return;

      try {
        // Get current user
        const { data: { user }, error: authError } = await supabase.auth.getUser();

        if (authError || !user) {
          setIsOwner(false);
          return;
        }

        // Check if current user owns this business by comparing user.id with business.id
        // In our system, business owners have user.id === business.id
        const isBusinessOwner = user.id === businessData.id;
        setIsOwner(isBusinessOwner);

        // Only load interaction status if user is not the owner
        if (!isBusinessOwner) {
          const statusResult = await getBusinessInteractionStatus(businessData.id);
          if (statusResult.success && statusResult.data) {
            setInteractionStatus(statusResult.data);
          }
        }
      } catch (error) {
        console.error('Error loading interaction status:', error);
        setIsOwner(false);
      }
    };

    loadInteractionStatus();
  }, [businessData.id]);

  const themeColor = businessData.theme_color || '#D4AF37';
  const iconColor = isDark ? '#9CA3AF' : '#6B7280';

  const renderTabContent = () => {
    if (loadingCardData) {
      return (
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="small" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      );
    }

    switch (selectedTab) {
      case 'about':
        return renderAboutTab();
      case 'products':
        return renderProductsTab();
      case 'gallery':
        return renderGalleryTab();
      case 'reviews':
        return renderReviewsTab();
      default:
        return renderAboutTab();
    }
  };

  const renderAboutTab = () => {
    const formatBusinessHours = () => {
      if (!businessData.business_hours || typeof businessData.business_hours !== 'object') {
        return [];
      }

      const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
      const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

      return days.map((day, index) => {
        const dayData = businessData.business_hours[day];
        const dayName = dayNames[index];

        if (!dayData || !dayData.isOpen) {
          return { day: dayName, hours: 'Closed' };
        }

        const formatTime = (time) => {
          const [hour, minute] = time.split(':');
          const hourNum = parseInt(hour);
          const period = hourNum >= 12 ? 'PM' : 'AM';
          const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;
          return `${displayHour}:${minute} ${period}`;
        };

        const openTime = formatTime(dayData.openTime);
        const closeTime = formatTime(dayData.closeTime);
        return { day: dayName, hours: `${openTime} - ${closeTime}` };
      });
    };

    const businessHoursData = formatBusinessHours();

    return (
      <ScrollView style={styles.section} showsVerticalScrollIndicator={false}>
        {/* About Bio */}
        {businessData.about_bio && (
          <View style={styles.aboutSection}>
            <Text style={styles.aboutText}>{businessData.about_bio}</Text>
          </View>
        )}

        {/* Business Information Section */}
        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Business Information</Text>
          <View style={styles.aboutTableContainer}>
            {businessData.business_name && (
              <View style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Package color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>Business Name</Text>
                </View>
                <Text style={styles.aboutTableValue}>{businessData.business_name}</Text>
              </View>
            )}

            {businessData.member_name && (
              <View style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Users color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>Owner Name</Text>
                </View>
                <Text style={styles.aboutTableValue}>{businessData.member_name}</Text>
              </View>
            )}

            {businessData.business_category && (
              <View style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Package color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>Category</Text>
                </View>
                <Text style={styles.aboutTableValue}>{businessData.business_category}</Text>
              </View>
            )}

            {businessData.established_year && (
              <View style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Calendar color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>Established</Text>
                </View>
                <Text style={styles.aboutTableValue}>{businessData.established_year}</Text>
              </View>
            )}

            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Clock color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Business Status</Text>
              </View>
              <View style={styles.statusBadge}>
                <Text style={styles.statusText}>Open</Text>
              </View>
            </View>
          </View>
        </View>

  // Initialize pagination data
  useEffect(() => {
    if (cardData?.products) {
      setAllProducts(cardData.products);
    }
    if (cardData?.reviews) {
      setAllReviews(cardData.reviews);
    }
  }, [cardData]);

  // Load more products
  const loadMoreProducts = async () => {
    if (loadingMoreProducts || !hasMoreProducts) return;

    setLoadingMoreProducts(true);
    try {
      const result = await fetchBusinessProductsPaginated(
        businessData.id,
        productsPage + 1,
        6
      );

      if (result.success && result.data) {
        setAllProducts(prev => [...prev, ...result.data!]);
        setProductsPage(prev => prev + 1);
        setHasMoreProducts(result.hasMore || false);
      }
    } catch (error) {
      console.error('Error loading more products:', error);
    } finally {
      setLoadingMoreProducts(false);
    }
  };

  // Load more reviews
  const loadMoreReviews = async () => {
    if (loadingMoreReviews || !hasMoreReviews) return;

    setLoadingMoreReviews(true);
    try {
      const result = await fetchBusinessReviewsPaginated(
        businessData.id,
        reviewsPage + 1,
        5
      );

      if (result.success && result.data) {
        setAllReviews(prev => [...prev, ...result.data!]);
        setReviewsPage(prev => prev + 1);
        setHasMoreReviews(result.hasMore || false);
      }
    } catch (error) {
      console.error('Error loading more reviews:', error);
    } finally {
      setLoadingMoreReviews(false);
    }
  };

  // Convert BusinessProduct to ProductData for compatibility
  const convertBusinessProductToProductData = (product: BusinessProduct) => {
    return {
      id: product.id,
      name: product.name,
      description: product.description || null,
      base_price: product.base_price || null,
      discounted_price: product.discounted_price || null,
      image_url: product.image_url || null,
      images: product.images || null,
      featured_image_index: product.featured_image_index || null,
      business_id: businessData.id,
      is_available: true,
      slug: product.slug || `product-${product.id}`,
    };
  };

  const renderProductsTab = () => (
    <View style={styles.section}>
      {allProducts && allProducts.length > 0 ? (
        <FlatList
          data={allProducts}
          keyExtractor={(item) => item.id}
          numColumns={2}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          columnWrapperStyle={{ justifyContent: 'space-between' }}
          ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
          onEndReached={loadMoreProducts}
          onEndReachedThreshold={0.1}
          ListFooterComponent={() =>
            loadingMoreProducts ? (
              <View style={{ padding: 20, alignItems: 'center' }}>
                <LoadingSpinner size="small" />
              </View>
            ) : null
          }
          renderItem={({ item }) => (
            <View style={{ flex: 0.48 }}>
              <ProductCard
                product={convertBusinessProductToProductData(item)}
                isClickable={true}
                variant="default"
              />
            </View>
          )}
        />
      ) : (
        <Text style={styles.emptyText}>No products available</Text>
      )}
    </View>
  );

  const handleGalleryImagePress = (index: number) => {
    setSelectedImageIndex(index);
    setFullScreenImageVisible(true);
  };

  const renderGalleryTab = () => (
    <View style={styles.section}>
      {cardData?.gallery && cardData.gallery.length > 0 ? (
        <FlatList
          data={cardData.gallery}
          keyExtractor={(item) => item.id}
          numColumns={2}
          scrollEnabled={false}
          renderItem={({ item, index }) => (
            <TouchableOpacity
              style={styles.galleryItem}
              onPress={() => handleGalleryImagePress(index)}
              activeOpacity={0.8}
            >
              <Image source={{ uri: item.url }} style={styles.galleryImage} />
            </TouchableOpacity>
          )}
        />
      ) : (
        <Text style={styles.emptyText}>No gallery images available</Text>
      )}
    </View>
  );

  const renderReviewsTab = () => (
    <View style={styles.section}>
      {cardData?.reviewStats && (
        <View style={styles.reviewStatsContainer}>
          <View style={styles.ratingOverview}>
            <Text style={styles.averageRating}>{cardData.reviewStats.averageRating.toFixed(1)}</Text>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  color={star <= cardData.reviewStats.averageRating ? '#f39c12' : '#ddd'}
                  fill={star <= cardData.reviewStats.averageRating ? '#f39c12' : 'transparent'}
                />
              ))}
            </View>
            <Text style={styles.totalReviews}>({formatIndianNumberShort(cardData.reviewStats.totalReviews)} reviews)</Text>
          </View>
        </View>
      )}

      {allReviews && allReviews.length > 0 ? (
        <FlatList
          data={allReviews}
          keyExtractor={(item) => item.id}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          onEndReached={loadMoreReviews}
          onEndReachedThreshold={0.1}
          ListFooterComponent={() =>
            loadingMoreReviews ? (
              <View style={{ padding: 20, alignItems: 'center' }}>
                <LoadingSpinner size="small" />
              </View>
            ) : null
          }
          renderItem={({ item }) => (
            <View style={styles.reviewCard}>
              <View style={styles.reviewHeader}>
                <View style={styles.reviewerInfo}>
                  <View style={styles.reviewerAvatar}>
                    <Text style={styles.reviewerInitial}>
                      {item.customer_name?.charAt(0).toUpperCase() || 'U'}
                    </Text>
                  </View>
                  <View>
                    <Text style={styles.reviewerName}>
                      {item.customer_name || 'Anonymous'}
                    </Text>
                    <View style={styles.reviewStars}>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          size={12}
                          color={star <= item.rating ? '#f39c12' : '#ddd'}
                          fill={star <= item.rating ? '#f39c12' : 'transparent'}
                        />
                      ))}
                    </View>
                  </View>
                </View>
                <Text style={styles.reviewDate}>
                  {new Date(item.created_at).toLocaleDateString()}
                </Text>
              </View>
              {item.review_text && (
                <Text style={styles.reviewText}>{item.review_text}</Text>
              )}
            </View>
          )}
        />
      ) : (
        <Text style={styles.emptyText}>No reviews available</Text>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Section with Theme Color */}
        <View style={[styles.header, { backgroundColor: themeColor }]}>
          {/* Back Button with Circular Background */}
          <TouchableOpacity
            style={styles.backButtonContainer}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <ArrowLeft color="#fff" size={24} />
          </TouchableOpacity>

          <View style={styles.logoContainer}>
            {businessData.logo_url && !imageError ? (
              <Image
                source={{ uri: businessData.logo_url }}
                style={styles.logo}
                onError={() => setImageError(true)}
              />
            ) : (
              <View style={[styles.logoPlaceholder, { backgroundColor: 'rgba(255,255,255,0.2)' }]}>
                <Text style={styles.logoText}>
                  {businessData.business_name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.headerInfo}>
            <Text style={styles.businessName}>{businessData.business_name}</Text>
          </View>
        </View>

        {/* Stats Section */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Heart color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
            <Text style={styles.statNumber}>
              {formatIndianNumberShort(interactionStatus?.likeCount ?? businessData.total_likes ?? 0)}
            </Text>
            <Text style={styles.statLabel}>Likes</Text>
          </View>
          <View style={styles.statItem}>
            <Users color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
            <Text style={styles.statNumber}>
              {formatIndianNumberShort(interactionStatus?.subscriptionCount ?? businessData.total_subscriptions ?? 0)}
            </Text>
            <Text style={styles.statLabel}>Followers</Text>
          </View>
          <View style={styles.statItem}>
            <Star color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
            <Text style={styles.statNumber}>
              {(interactionStatus?.averageRating ?? businessData.average_rating ?? 0).toFixed(1)}
            </Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
        </View>

        {/* Stylish Floating Interaction Buttons */}
        {!isOwner && (
          <View style={styles.floatingInteractionButtons}>
            {/* Like Button */}
            <View style={styles.floatingButtonContainer}>
              <Animated.View style={{ transform: [{ scale: likeScale }] }}>
                <TouchableOpacity
                  style={[
                    styles.floatingButton,
                    styles.likeFloatingButton,
                    interactionStatus?.isLiked && styles.activeFloatingButton
                  ]}
                  onPress={handleLikePress}
                  activeOpacity={0.7}
                  disabled={likeLoading}
                >
                  {/* Inner circle for better visual depth */}
                  <View style={{
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: interactionStatus?.isLiked ? '#E74C3C' : 'transparent',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: interactionStatus?.isLiked ? 0 : 1,
                    borderColor: '#E74C3C',
                  }}>
                    {likeLoading ? (
                      <Animated.View
                        style={{
                          transform: [{
                            rotate: likeSpinValue.interpolate({
                              inputRange: [0, 1],
                              outputRange: ['0deg', '360deg'],
                            }),
                          }],
                        }}
                      >
                        <Loader2 size={16} color={interactionStatus?.isLiked ? "#FFF" : "#E74C3C"} />
                      </Animated.View>
                    ) : (
                      <Heart
                        size={16}
                        color={interactionStatus?.isLiked ? "#FFF" : "#E74C3C"}
                        fill={interactionStatus?.isLiked ? "#FFF" : "transparent"}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              </Animated.View>
              <Text style={styles.buttonLabel}>
                {interactionStatus?.isLiked ? 'Liked' : 'Like'}
              </Text>
            </View>

            {/* Subscribe Button */}
            <View style={styles.floatingButtonContainer}>
              <Animated.View style={{ transform: [{ scale: subscribeScale }] }}>
                <TouchableOpacity
                  style={[
                    styles.floatingButton,
                    styles.subscribeFloatingButton,
                    interactionStatus?.isSubscribed && styles.activeFloatingButton
                  ]}
                  onPress={handleSubscribePress}
                  activeOpacity={0.7}
                  disabled={subscribeLoading}
                >
                  {/* Inner circle for better visual depth */}
                  <View style={{
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: interactionStatus?.isSubscribed ? '#3498DB' : 'transparent',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: interactionStatus?.isSubscribed ? 0 : 1,
                    borderColor: '#3498DB',
                  }}>
                    {subscribeLoading ? (
                      <Animated.View
                        style={{
                          transform: [{
                            rotate: subscribeSpinValue.interpolate({
                              inputRange: [0, 1],
                              outputRange: ['0deg', '360deg'],
                            }),
                          }],
                        }}
                      >
                        <Loader2 size={16} color={interactionStatus?.isSubscribed ? "#FFF" : "#3498DB"} />
                      </Animated.View>
                    ) : (
                      <Users
                        size={16}
                        color={interactionStatus?.isSubscribed ? "#FFF" : "#3498DB"}
                        fill={interactionStatus?.isSubscribed ? "#FFF" : "transparent"}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              </Animated.View>
              <Text style={styles.buttonLabel}>
                {interactionStatus?.isSubscribed ? 'Following' : 'Follow'}
              </Text>
            </View>

            {/* Review Button */}
            <View style={styles.floatingButtonContainer}>
              <Animated.View style={{ transform: [{ scale: reviewScale }] }}>
                <TouchableOpacity
                  style={[
                    styles.floatingButton,
                    styles.reviewFloatingButton,
                    interactionStatus?.userRating ? styles.activeFloatingButton : null
                  ]}
                  onPress={handleReviewPress}
                  activeOpacity={0.7}
                >
                  {/* Inner circle for better visual depth */}
                  <View style={{
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: interactionStatus?.userRating ? '#F39C12' : 'transparent',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: interactionStatus?.userRating ? 0 : 1,
                    borderColor: '#F39C12',
                  }}>
                    <Star
                      size={16}
                      color={interactionStatus?.userRating ? "#FFF" : "#F39C12"}
                      fill={interactionStatus?.userRating ? "#FFF" : "transparent"}
                    />
                  </View>
                </TouchableOpacity>
              </Animated.View>
              <Text style={styles.buttonLabel}>
                {interactionStatus?.userRating ? 'Reviewed' : 'Review'}
              </Text>
            </View>
          </View>
        )}

        {/* Contact Action Buttons - Call Now and Message Now */}
        <View style={styles.contactButtonsSection}>
          {businessData.phone && (
            <TouchableOpacity style={styles.callButton} onPress={handlePhonePress}>
              <Phone color="#fff" size={18} />
              <Text style={styles.callButtonText}>Call Now</Text>
            </TouchableOpacity>
          )}
          {businessData.whatsapp_number && (
            <TouchableOpacity style={styles.messageButton} onPress={handleWhatsAppPress}>
              <MessageCircle color="#fff" size={18} />
              <Text style={styles.messageButtonText}>Message Now</Text>
            </TouchableOpacity>
          )}
        </View>



      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        {businessData.instagram_url && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#E4405F' }]}
            onPress={handleInstagramPress}
          >
            <Instagram color="#fff" size={20} />
            <Text style={styles.actionButtonText}>Instagram</Text>
          </TouchableOpacity>
        )}

        {businessData.facebook_url && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#1877F2' }]}
            onPress={handleFacebookPress}
          >
            <Facebook color="#fff" size={20} />
            <Text style={styles.actionButtonText}>Facebook</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'about' && styles.activeTab]}
          onPress={() => setSelectedTab('about')}
        >
          <Text style={[styles.tabText, selectedTab === 'about' && styles.activeTabText]}>About</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'products' && styles.activeTab]}
          onPress={() => setSelectedTab('products')}
        >
          <Package color={selectedTab === 'products' ? '#D4AF37' : (isDark ? '#A0A0A0' : '#6B7280')} size={16} />
          <Text style={[styles.tabText, selectedTab === 'products' && styles.activeTabText]}>Products</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'gallery' && styles.activeTab]}
          onPress={() => setSelectedTab('gallery')}
        >
          <ImageIcon color={selectedTab === 'gallery' ? '#D4AF37' : (isDark ? '#A0A0A0' : '#6B7280')} size={16} />
          <Text style={[styles.tabText, selectedTab === 'gallery' && styles.activeTabText]}>Gallery</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'reviews' && styles.activeTab]}
          onPress={() => setSelectedTab('reviews')}
        >
          <MessageSquare color={selectedTab === 'reviews' ? '#D4AF37' : (isDark ? '#A0A0A0' : '#6B7280')} size={16} />
          <Text style={[styles.tabText, selectedTab === 'reviews' && styles.activeTabText]}>Reviews</Text>
        </TouchableOpacity>
      </View>

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>

      {/* Review Modal */}
      <ReviewModal
        visible={reviewModalVisible}
        onClose={() => setReviewModalVisible(false)}
        businessId={businessData.id}
        businessName={businessData.business_name}
        existingRating={interactionStatus?.userRating}
        existingReview={interactionStatus?.userReview}
        onReviewSubmitted={handleReviewSubmitted}
      />

      {/* Full Screen Image Viewer */}
      <FullScreenImageViewer
        visible={fullScreenImageVisible}
        images={cardData?.gallery?.map(img => ({ id: img.id, url: img.url })) || []}
        initialIndex={selectedImageIndex}
        onClose={() => setFullScreenImageVisible(false)}
      />
    </View>
  );
}


