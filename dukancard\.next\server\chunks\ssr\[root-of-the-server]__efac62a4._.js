module.exports = {

"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
;
;
async function createClient() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o"), {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}}),
"[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminClient": (()=>createAdminClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-rsc] (ecmascript) <locals>");
;
function createAdminClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
}
}}),
"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4017775fac46adac9e7b1dbbdf89224814836b0bd5":"markActivitiesAsRead","40a5ca69f200099caf454118f78daec53970a44d23":"getBusinessActivities","40e8d26551daec16eb75e419b18aa90c74990473ee":"getUnreadActivitiesCount"},"",""] */ __turbopack_context__.s({
    "getBusinessActivities": (()=>getBusinessActivities),
    "getUnreadActivitiesCount": (()=>getUnreadActivitiesCount),
    "markActivitiesAsRead": (()=>markActivitiesAsRead)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function getBusinessActivities({ businessProfileId, page = 1, pageSize = 15, sortBy = "newest", filterBy = "all", autoMarkAsRead = true }) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return {
            activities: [],
            count: 0,
            error: "Not authenticated"
        };
    }
    // Verify the user is the owner of the business
    if (user.id !== businessProfileId) {
        return {
            activities: [],
            count: 0,
            error: "Unauthorized"
        };
    }
    try {
        // Calculate pagination
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        // Build the query
        let query = supabase.from("business_activities").select("*", {
            count: "exact"
        }).eq("business_profile_id", businessProfileId);
        // Apply filter
        if (filterBy === "like") {
            query = query.eq("activity_type", "like");
        } else if (filterBy === "subscribe") {
            query = query.eq("activity_type", "subscribe");
        } else if (filterBy === "rating") {
            query = query.eq("activity_type", "rating");
        } else if (filterBy === "unread") {
            query = query.eq("is_read", false);
        }
        // Apply sorting
        switch(sortBy){
            case "oldest":
                query = query.order("created_at", {
                    ascending: true
                });
                break;
            case "unread_first":
                query = query.order("is_read", {
                    ascending: true
                }).order("created_at", {
                    ascending: false
                });
                break;
            case "newest":
            default:
                query = query.order("created_at", {
                    ascending: false
                });
                break;
        }
        // Apply pagination
        query = query.range(from, to);
        // Execute the query
        const { data: activities, error, count } = await query;
        if (error) {
            console.error("Error fetching business activities:", error);
            return {
                activities: [],
                count: 0,
                error: error.message
            };
        }
        // Get user profiles for the activities
        const userIds = activities.map((activity)=>activity.user_id);
        // Fetch both customer and business profiles
        const [customerProfiles, businessProfiles] = await Promise.all([
            supabaseAdmin.from("customer_profiles").select("id, name, avatar_url, email").in("id", userIds),
            supabaseAdmin.from("business_profiles").select("id, business_name, business_slug, logo_url").in("id", userIds)
        ]);
        // Combine the profiles
        const userProfiles = new Map();
        // Add customer profiles to the map
        customerProfiles.data?.forEach((profile)=>{
            userProfiles.set(profile.id, {
                name: profile.name,
                avatar_url: profile.avatar_url,
                email: profile.email,
                is_business: false
            });
        });
        // Add business profiles to the map, overriding customer profiles if both exist
        businessProfiles.data?.forEach((profile)=>{
            const existingProfile = userProfiles.get(profile.id) || {};
            userProfiles.set(profile.id, {
                ...existingProfile,
                business_name: profile.business_name,
                business_slug: profile.business_slug,
                logo_url: profile.logo_url,
                is_business: true
            });
        });
        // Attach user profiles to activities
        const activitiesWithProfiles = activities.map((activity)=>({
                ...activity,
                user_profile: userProfiles.get(activity.user_id) || {}
            }));
        // Auto-mark fetched activities as read if enabled
        if (autoMarkAsRead && activities.length > 0) {
            // Get IDs of unread activities
            const unreadActivityIds = activities.filter((activity)=>!activity.is_read).map((activity)=>activity.id);
            // Only proceed if there are unread activities
            if (unreadActivityIds.length > 0) {
                // Mark these activities as read
                const { error: markError } = await supabase.from("business_activities").update({
                    is_read: true
                }).eq("business_profile_id", businessProfileId).in("id", unreadActivityIds);
                if (markError) {
                    console.error("Error auto-marking activities as read:", markError);
                } else {
                    // Update the activities in our result to reflect they're now read
                    activitiesWithProfiles.forEach((activity)=>{
                        if (unreadActivityIds.includes(activity.id)) {
                            activity.is_read = true;
                        }
                    });
                }
            }
        }
        return {
            activities: activitiesWithProfiles,
            count: count || 0,
            error: null
        };
    } catch (error) {
        console.error("Unexpected error fetching business activities:", error);
        return {
            activities: [],
            count: 0,
            error: "An unexpected error occurred"
        };
    }
}
async function markActivitiesAsRead({ businessProfileId, activityIds }) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return {
            success: false,
            error: "Not authenticated"
        };
    }
    // Verify the user is the owner of the business
    if (user.id !== businessProfileId) {
        return {
            success: false,
            error: "Unauthorized"
        };
    }
    try {
        // If marking specific activities as read
        if (activityIds !== "all") {
            // Handle case where we have specific activity IDs
            const { error } = await supabase.from("business_activities").update({
                is_read: true
            }).eq("business_profile_id", businessProfileId).in("id", activityIds);
            if (error) {
                console.error("Error marking specific activities as read:", error);
                return {
                    success: false,
                    error: error.message
                };
            }
        } else {
            // Handle "mark all as read" with pagination to work around Supabase's 1000 row limit
            const BATCH_SIZE = 1000; // Maximum number of rows to update at once
            let hasMore = true;
            let processedCount = 0;
            while(hasMore){
                // Get a batch of unread activity IDs
                const { data: unreadActivities, error: fetchError } = await supabase.from("business_activities").select("id").eq("business_profile_id", businessProfileId).eq("is_read", false).limit(BATCH_SIZE);
                if (fetchError) {
                    console.error("Error fetching unread activities:", fetchError);
                    return {
                        success: false,
                        error: fetchError.message
                    };
                }
                // If no more unread activities, we're done
                if (!unreadActivities || unreadActivities.length === 0) {
                    hasMore = false;
                    break;
                }
                // Extract IDs from the batch
                const batchIds = unreadActivities.map((activity)=>activity.id);
                // Mark this batch as read
                const { error: updateError } = await supabase.from("business_activities").update({
                    is_read: true
                }).eq("business_profile_id", businessProfileId).in("id", batchIds);
                if (updateError) {
                    console.error("Error marking batch as read:", updateError);
                    return {
                        success: false,
                        error: updateError.message
                    };
                }
                // Update processed count and check if we need to continue
                processedCount += batchIds.length;
                hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more
            }
            console.log(`Marked ${processedCount} activities as read`);
        }
        // Revalidate the activities page
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/activities");
        return {
            success: true,
            error: null
        };
    } catch (error) {
        console.error("Unexpected error marking activities as read:", error);
        return {
            success: false,
            error: "An unexpected error occurred"
        };
    }
}
async function getUnreadActivitiesCount(businessProfileId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return {
            count: 0,
            error: "Not authenticated"
        };
    }
    // Verify the user is the owner of the business
    if (user.id !== businessProfileId) {
        return {
            count: 0,
            error: "Unauthorized"
        };
    }
    try {
        const { count, error } = await supabase.from("business_activities").select("*", {
            count: "exact",
            head: true
        }).eq("business_profile_id", businessProfileId).eq("is_read", false);
        if (error) {
            console.error("Error getting unread activities count:", error);
            return {
                count: 0,
                error: error.message
            };
        }
        return {
            count: count || 0,
            error: null
        };
    } catch (error) {
        console.error("Unexpected error getting unread activities count:", error);
        return {
            count: 0,
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getBusinessActivities,
    markActivitiesAsRead,
    getUnreadActivitiesCount
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessActivities, "40a5ca69f200099caf454118f78daec53970a44d23", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(markActivitiesAsRead, "4017775fac46adac9e7b1dbbdf89224814836b0bd5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getUnreadActivitiesCount, "40e8d26551daec16eb75e419b18aa90c74990473ee", null);
}}),
"[project]/app/auth/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00b5b8ef95d3d0ba8e30b0dfcedbc6f2be25e07e63":"signOutUser"},"",""] */ __turbopack_context__.s({
    "signOutUser": (()=>signOutUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function signOutUser() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error: _error } = await supabase.auth.signOut();
        // Note: Sign out errors are typically not critical for user experience
        // The user will be redirected to login regardless
        // Explicitly clear auth cookies to ensure logout
        const cookieStore = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.cookies());
        const cookiesToClear = [
            "sb-access-token",
            "sb-refresh-token"
        ];
        for (const cookieName of cookiesToClear){
            try {
                cookieStore.set(cookieName, "", {
                    expires: new Date(0),
                    maxAge: -1
                });
            } catch  {
            // Cookie clearing errors are not critical for sign out
            // Continue with the sign out process
            }
        }
    } catch  {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
    }
    // Redirect to login with a flag to prevent middleware redirect loop
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?logged_out=true");
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signOutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signOutUser, "00b5b8ef95d3d0ba8e30b0dfcedbc6f2be25e07e63", null);
}}),
"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4044c2d63ef50e6aed243c22339f1fbcd25e807b72":"fetchProductsByIds"},"",""] */ __turbopack_context__.s({
    "fetchProductsByIds": (()=>fetchProductsByIds)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function fetchProductsByIds(productIds) {
    if (!productIds || productIds.length === 0) {
        return {
            success: true,
            data: []
        };
    }
    try {
        // Use admin client to bypass RLS policies
        const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        const { data, error } = await supabaseAdmin.from('products_services').select('id, name, base_price, discounted_price, image_url, slug').in('id', productIds).eq('is_available', true);
        if (error) {
            console.error('Error fetching products by IDs:', error);
            return {
                success: false,
                error: 'Failed to fetch products'
            };
        }
        return {
            success: true,
            data: data || []
        };
    } catch (error) {
        console.error('Error in fetchProductsByIds:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchProductsByIds
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchProductsByIds, "4044c2d63ef50e6aed243c22339f1fbcd25e807b72", null);
}}),
"[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Scalable Storage Path Utilities
 *
 * This module provides utilities for generating scalable storage paths
 * that can handle billions of users efficiently using hash-based distribution.
 */ /**
 * Generate scalable user path using hash-based distribution
 *
 * @param userId - The user's UUID
 * @returns Scalable path: users/{prefix}/{midfix}/{userId}
 *
 * Example:
 * - Input: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 * - Output: "users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 */ __turbopack_context__.s({
    "PathValidator": (()=>PathValidator),
    "StorageAnalytics": (()=>StorageAnalytics),
    "getCustomAdImagePath": (()=>getCustomAdImagePath),
    "getCustomHeaderImagePath": (()=>getCustomHeaderImagePath),
    "getCustomerAvatarPath": (()=>getCustomerAvatarPath),
    "getCustomerPostImagePath": (()=>getCustomerPostImagePath),
    "getGalleryImagePath": (()=>getGalleryImagePath),
    "getPostFolderPath": (()=>getPostFolderPath),
    "getPostImagePath": (()=>getPostImagePath),
    "getProductBaseImagePath": (()=>getProductBaseImagePath),
    "getProductImagePath": (()=>getProductImagePath),
    "getProductVariantImagePath": (()=>getProductVariantImagePath),
    "getProfileImagePath": (()=>getProfileImagePath),
    "getScalableUserPath": (()=>getScalableUserPath),
    "getThemeSpecificHeaderImagePath": (()=>getThemeSpecificHeaderImagePath)
});
function getScalableUserPath(userId) {
    if (!userId || typeof userId !== 'string') {
        throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);
    }
    if (userId.length < 4) {
        throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);
    }
    const prefix = userId.substring(0, 2).toLowerCase();
    const midfix = userId.substring(2, 4).toLowerCase();
    return `users/${prefix}/${midfix}/${userId}`;
}
function getProfileImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/profile/logo_${timestamp}.webp`;
}
function getProductImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;
}
function getProductBaseImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;
}
function getProductVariantImagePath(userId, productId, variantId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;
}
function getGalleryImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/gallery/gallery_${timestamp}.webp`;
}
function getPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getPostFolderPath(userId, postId, createdAt) {
    const userPath = getScalableUserPath(userId);
    const postDate = new Date(createdAt);
    const year = postDate.getFullYear();
    const month = String(postDate.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}`;
}
function getCustomerAvatarPath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/avatar/avatar_${timestamp}.webp`;
}
function getCustomerPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getCustomAdImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/ads/custom_ad_${timestamp}.webp`;
}
function getCustomHeaderImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${timestamp}.webp`;
}
function getThemeSpecificHeaderImagePath(userId, timestamp, theme) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${theme}_${timestamp}.webp`;
}
class PathValidator {
    /**
   * Validate if a path follows the new scalable structure
   */ static isScalablePath(path) {
        return path.startsWith('users/') && path.split('/').length >= 4;
    }
    /**
   * Extract user ID from scalable path
   */ static extractUserIdFromPath(path) {
        if (!this.isScalablePath(path)) {
            return null;
        }
        const parts = path.split('/');
        return parts[3]; // users/{prefix}/{midfix}/{userId}/...
    }
    /**
   * Validate path structure integrity
   */ static validatePathStructure(userId, path) {
        const expectedUserPath = getScalableUserPath(userId);
        return path.startsWith(expectedUserPath);
    }
}
class StorageAnalytics {
    /**
   * Get storage distribution info for monitoring
   */ static getDistributionInfo(userId) {
        const prefix = userId.substring(0, 2).toLowerCase();
        const midfix = userId.substring(2, 4).toLowerCase();
        // Estimate number of users in same bucket (assuming even distribution)
        const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets
        const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users
        return {
            prefix,
            midfix,
            bucket: `${prefix}/${midfix}`,
            estimatedPeers
        };
    }
}
}}),
"[project]/lib/actions/shared/upload-post-media.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7002fcf3f41ad2ff47ee7aaa90079d632ed88006f4":"uploadPostImage","70111ee7bd03ebe9160558d30abb546069aed7c846":"deletePostMedia"},"",""] */ __turbopack_context__.s({
    "deletePostMedia": (()=>deletePostMedia),
    "uploadPostImage": (()=>uploadPostImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function uploadPostImage(formData, postId, postCreatedAt) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    const userId = user.id;
    const imageFile = formData.get("imageFile");
    if (!imageFile) {
        return {
            success: false,
            error: "No image file provided."
        };
    }
    // Use the provided post creation date for consistent folder structure
    // Validate file type (strict backend validation)
    const allowedTypes = [
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/webp"
    ];
    const allowedExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".webp"
    ];
    if (!allowedTypes.includes(imageFile.type)) {
        return {
            success: false,
            error: "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."
        };
    }
    // Validate file extension as additional security measure
    const fileName = imageFile.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some((ext)=>fileName.endsWith(ext));
    if (!hasValidExtension) {
        return {
            success: false,
            error: "Invalid file extension. Please upload files with .jpg, .jpeg, .png, .gif, or .webp extensions."
        };
    }
    // Backend size validation (critical security check)
    const maxSize = 15 * 1024 * 1024; // 15MB - matches industry standards
    if (imageFile.size > maxSize) {
        const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(2);
        return {
            success: false,
            error: `File size (${fileSizeMB}MB) exceeds the 15MB limit. Please choose a smaller image.`
        };
    }
    // Additional security: Check for minimum file size (avoid empty files)
    if (imageFile.size < 100) {
        return {
            success: false,
            error: "File appears to be empty or corrupted. Please try a different image."
        };
    }
    try {
        // Create scalable path structure for billions of users
        const timestamp = Date.now() + Math.floor(Math.random() * 1000);
        const bucketName = "business";
        const imagePath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPostImagePath"])(userId, postId, 0, timestamp, postCreatedAt);
        // File is already compressed on client-side, just upload it
        const fileBuffer = Buffer.from(await imageFile.arrayBuffer());
        // Use admin client for storage operations to bypass RLS
        const adminSupabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Upload to Supabase Storage using admin client
        const { error: uploadError } = await adminSupabase.storage.from(bucketName).upload(imagePath, fileBuffer, {
            contentType: imageFile.type,
            upsert: true
        });
        if (uploadError) {
            console.error("Post Image Upload Error:", uploadError);
            return {
                success: false,
                error: `Failed to upload image: ${uploadError.message}`
            };
        }
        // Get the public URL using admin client
        const { data: urlData } = adminSupabase.storage.from(bucketName).getPublicUrl(imagePath);
        if (!urlData?.publicUrl) {
            return {
                success: false,
                error: "Could not retrieve public URL after upload."
            };
        }
        return {
            success: true,
            url: urlData.publicUrl
        };
    } catch (error) {
        console.error("Error processing post image:", error);
        return {
            success: false,
            error: "Failed to process image. Please try a different image."
        };
    }
}
async function deletePostMedia(userId, postId, createdAt) {
    // Use admin client for storage operations to bypass RLS
    const adminSupabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
    try {
        const bucketName = "business";
        const postFolderPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPostFolderPath"])(userId, postId, createdAt);
        // List all files in the post folder
        const { data: files, error: listError } = await adminSupabase.storage.from(bucketName).list(postFolderPath, {
            limit: 1000,
            sortBy: {
                column: 'name',
                order: 'asc'
            }
        });
        if (listError) {
            // If folder doesn't exist, consider it successful (already deleted)
            if (listError.message?.includes('not found') || listError.message?.includes('does not exist') || listError.message?.includes('The resource was not found')) {
                return {
                    success: true
                };
            }
            console.error("Error listing post media files:", listError);
            return {
                success: false,
                error: `Failed to list media files: ${listError.message}`
            };
        }
        if (!files || files.length === 0) {
            // No files to delete, folder is already empty or doesn't exist
            return {
                success: true
            };
        }
        // Create full paths for all files in the folder
        const filePaths = files.map((file)=>`${postFolderPath}/${file.name}`);
        // Delete all files in the post folder using admin client
        // In object storage, deleting all files effectively removes the folder
        const { error: deleteError } = await adminSupabase.storage.from(bucketName).remove(filePaths);
        if (deleteError) {
            console.error("Error deleting post folder contents:", deleteError);
            return {
                success: false,
                error: `Failed to delete post folder: ${deleteError.message}`
            };
        }
        return {
            success: true
        };
    } catch (error) {
        console.error("Error in deletePostMedia:", error);
        return {
            success: false,
            error: "An unexpected error occurred while deleting post folder."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    uploadPostImage,
    deletePostMedia
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(uploadPostImage, "7002fcf3f41ad2ff47ee7aaa90079d632ed88006f4", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deletePostMedia, "70111ee7bd03ebe9160558d30abb546069aed7c846", null);
}}),
"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40d09a6de0a98d0c3d2475e2b600f5486900620cf8":"deletePost","40dbf6c29c2d78a40c03a866d1b3d2bbac346df74a":"createPost","6056ee791b5ea7c33617a0d1efee3fc91ab2a639c5":"updatePostContent","6074602f4e3db00fc9ea2b780a209226b15a0411ea":"updatePostProducts","60bb9b03f42518a37c9be2d8f3b0d6ea01847ba65c":"updatePost"},"",""] */ __turbopack_context__.s({
    "createPost": (()=>createPost),
    "deletePost": (()=>deletePost),
    "updatePost": (()=>updatePost),
    "updatePostContent": (()=>updatePostContent),
    "updatePostProducts": (()=>updatePostProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/upload-post-media.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function createPost(formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to create a post'
        };
    }
    // Get the user's business profile
    const { data: businessProfile, error: profileError } = await supabase.from('business_profiles').select('id, city_slug, state_slug, locality_slug, pincode').eq('id', user.id).single();
    if (profileError || !businessProfile) {
        return {
            success: false,
            message: 'Business profile not found',
            error: 'You must have a business profile to create a post'
        };
    }
    // Prepare post data
    const postData = {
        business_id: user.id,
        content: formData.content,
        image_url: formData.image_url || null,
        city_slug: businessProfile.city_slug,
        state_slug: businessProfile.state_slug,
        locality_slug: businessProfile.locality_slug,
        pincode: businessProfile.pincode,
        product_ids: formData.product_ids || [],
        mentioned_business_ids: formData.mentioned_business_ids || []
    };
    // Insert the post
    const { data, error } = await supabase.from('business_posts').insert(postData).select().single();
    if (error) {
        console.error('Error creating post:', error);
        return {
            success: false,
            message: 'Failed to create post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    return {
        success: true,
        message: 'Post created successfully',
        data
    };
}
async function updatePostContent(postId, content) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to update a post'
        };
    }
    // Check if the post exists and belongs to the user
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to update it'
        };
    }
    // Update only the content and timestamp
    const { data, error } = await supabase.from('business_posts').update({
        content: content.trim(),
        updated_at: new Date().toISOString()
    }).eq('id', postId).select().single();
    if (error) {
        console.error('Error updating post content:', error);
        return {
            success: false,
            message: 'Failed to update post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post updated successfully',
        data
    };
}
async function updatePostProducts(postId, productIds) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to update a post'
        };
    }
    // Check if the post exists and belongs to the user
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to update it'
        };
    }
    // Update only the product_ids and timestamp
    const { data, error } = await supabase.from('business_posts').update({
        product_ids: productIds,
        updated_at: new Date().toISOString()
    }).eq('id', postId).select().single();
    if (error) {
        console.error('Error updating post products:', error);
        return {
            success: false,
            message: 'Failed to update post products',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post products updated successfully',
        data
    };
}
async function updatePost(postId, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to update a post'
        };
    }
    // Check if the post exists and belongs to the user
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to update it'
        };
    }
    // Prepare update data
    const updateData = {
        content: formData.content,
        image_url: formData.image_url || null,
        product_ids: formData.product_ids || [],
        mentioned_business_ids: formData.mentioned_business_ids || [],
        updated_at: new Date().toISOString()
    };
    // Update the post
    const { data, error } = await supabase.from('business_posts').update(updateData).eq('id', postId).select().single();
    if (error) {
        console.error('Error updating post:', error);
        return {
            success: false,
            message: 'Failed to update post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post updated successfully',
        data
    };
}
async function deletePost(postId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to delete a post'
        };
    }
    // Check if the post exists and belongs to the user, get creation date for media deletion
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id, created_at, image_url').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to delete it'
        };
    }
    // Always attempt to delete the post folder from storage
    // This ensures we clean up any files that might exist, regardless of image_url status
    try {
        const mediaDeleteResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deletePostMedia"])(user.id, postId, existingPost.created_at);
        if (!mediaDeleteResult.success && mediaDeleteResult.error) {
            console.error('Error deleting post media:', mediaDeleteResult.error);
        // Continue with post deletion even if media deletion fails
        }
    } catch (mediaError) {
        console.error('Error deleting post media:', mediaError);
    // Continue with post deletion even if media deletion fails
    }
    // Delete the post
    const { error } = await supabase.from('business_posts').delete().eq('id', postId);
    if (error) {
        console.error('Error deleting post:', error);
        return {
            success: false,
            message: 'Failed to delete post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post deleted successfully'
    };
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    createPost,
    updatePostContent,
    updatePostProducts,
    updatePost,
    deletePost
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createPost, "40dbf6c29c2d78a40c03a866d1b3d2bbac346df74a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePostContent, "6056ee791b5ea7c33617a0d1efee3fc91ab2a639c5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePostProducts, "6074602f4e3db00fc9ea2b780a209226b15a0411ea", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePost, "60bb9b03f42518a37c9be2d8f3b0d6ea01847ba65c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deletePost, "40d09a6de0a98d0c3d2475e2b600f5486900620cf8", null);
}}),
"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"407ba87de4ca16d5fb7ed1a57c146fcd6b3e9a5ae1":"getSelectedProducts","40a788bd0129ed104e004a10b6f5a8408570e96886":"searchBusinessProducts"},"",""] */ __turbopack_context__.s({
    "getSelectedProducts": (()=>getSelectedProducts),
    "searchBusinessProducts": (()=>searchBusinessProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function searchBusinessProducts(query) {
    try {
        // Validate input
        if (!query || query.trim().length < 2) {
            return {
                success: false,
                error: 'Search query must be at least 2 characters long'
            };
        }
        // Get authenticated user
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return {
                success: false,
                error: 'Authentication required'
            };
        }
        // Use admin client to bypass RLS policies
        const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Search products for the current user's business only
        const { data, error } = await supabaseAdmin.from('products_services').select('id, name, base_price, discounted_price, image_url, slug').eq('business_id', user.id) // Ensure user can only see their own products
        .eq('is_available', true).ilike('name', `%${query.trim()}%`).order('name', {
            ascending: true
        }).limit(10); // Limit search results to 10 items
        if (error) {
            console.error('Error searching products:', error);
            return {
                success: false,
                error: 'Failed to search products'
            };
        }
        return {
            success: true,
            data: data || []
        };
    } catch (error) {
        console.error('Error in searchBusinessProducts:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function getSelectedProducts(productIds) {
    try {
        // Validate input
        if (!productIds || productIds.length === 0) {
            return {
                success: true,
                data: []
            };
        }
        // Get authenticated user
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return {
                success: false,
                error: 'Authentication required'
            };
        }
        // Use admin client to bypass RLS policies
        const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Get products by IDs, but only for the current user's business
        const { data, error } = await supabaseAdmin.from('products_services').select('id, name, base_price, discounted_price, image_url, slug').in('id', productIds).eq('business_id', user.id) // Ensure user can only access their own products
        .order('name', {
            ascending: true
        });
        if (error) {
            console.error('Error getting selected products:', error);
            return {
                success: false,
                error: 'Failed to get selected products'
            };
        }
        return {
            success: true,
            data: data || []
        };
    } catch (error) {
        console.error('Error in getSelectedProducts:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    searchBusinessProducts,
    getSelectedProducts
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(searchBusinessProducts, "40a788bd0129ed104e004a10b6f5a8408570e96886", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSelectedProducts, "407ba87de4ca16d5fb7ed1a57c146fcd6b3e9a5ae1", null);
}}),
"[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/activities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/activities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00b5b8ef95d3d0ba8e30b0dfcedbc6f2be25e07e63": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signOutUser"]),
    "4044c2d63ef50e6aed243c22339f1fbcd25e807b72": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchProductsByIds"]),
    "407ba87de4ca16d5fb7ed1a57c146fcd6b3e9a5ae1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSelectedProducts"]),
    "40a788bd0129ed104e004a10b6f5a8408570e96886": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["searchBusinessProducts"]),
    "40d09a6de0a98d0c3d2475e2b600f5486900620cf8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deletePost"]),
    "40e8d26551daec16eb75e419b18aa90c74990473ee": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getUnreadActivitiesCount"]),
    "60bb9b03f42518a37c9be2d8f3b0d6ea01847ba65c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updatePost"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/activities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00b5b8ef95d3d0ba8e30b0dfcedbc6f2be25e07e63": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00b5b8ef95d3d0ba8e30b0dfcedbc6f2be25e07e63"]),
    "4044c2d63ef50e6aed243c22339f1fbcd25e807b72": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4044c2d63ef50e6aed243c22339f1fbcd25e807b72"]),
    "407ba87de4ca16d5fb7ed1a57c146fcd6b3e9a5ae1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["407ba87de4ca16d5fb7ed1a57c146fcd6b3e9a5ae1"]),
    "40a788bd0129ed104e004a10b6f5a8408570e96886": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40a788bd0129ed104e004a10b6f5a8408570e96886"]),
    "40d09a6de0a98d0c3d2475e2b600f5486900620cf8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40d09a6de0a98d0c3d2475e2b600f5486900620cf8"]),
    "40e8d26551daec16eb75e419b18aa90c74990473ee": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40e8d26551daec16eb75e419b18aa90c74990473ee"]),
    "60bb9b03f42518a37c9be2d8f3b0d6ea01847ba65c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60bb9b03f42518a37c9be2d8f3b0d6ea01847ba65c"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$post$2f5b$postId$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/post/[postId]/error.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/post/[postId]/error.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/post/[postId]/loading.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/post/[postId]/loading.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/post/[postId]/not-found.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/post/[postId]/not-found.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/lib/actions/posts/fetchSinglePost.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchSinglePost": (()=>fetchSinglePost)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
;
async function fetchSinglePost(postId, retryCount = 0) {
    const maxRetries = 3;
    try {
        // Validate input
        if (!postId || typeof postId !== 'string') {
            return {
                success: false,
                message: 'Invalid post ID provided',
                error: 'INVALID_POST_ID'
            };
        }
        // Create Supabase client
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch post from unified_posts view
        const { data, error } = await supabase.from('unified_posts').select('*').eq('id', postId).single();
        if (error) {
            console.error('Error fetching single post:', error);
            // Handle specific error cases
            if (error.code === 'PGRST116') {
                return {
                    success: false,
                    message: 'Post not found',
                    error: 'POST_NOT_FOUND'
                };
            }
            return {
                success: false,
                message: 'Failed to fetch post',
                error: error.message
            };
        }
        if (!data) {
            return {
                success: false,
                message: 'Post not found',
                error: 'POST_NOT_FOUND'
            };
        }
        // Validate post data structure
        const post = {
            id: data.id,
            post_source: data.post_source,
            author_id: data.author_id,
            content: data.content || '',
            image_url: data.image_url,
            created_at: data.created_at,
            updated_at: data.updated_at,
            city_slug: data.city_slug,
            state_slug: data.state_slug,
            locality_slug: data.locality_slug,
            pincode: data.pincode,
            product_ids: data.product_ids || [],
            mentioned_business_ids: data.mentioned_business_ids || [],
            author_name: data.author_name,
            author_avatar: data.author_avatar,
            business_slug: data.business_slug,
            phone: data.phone,
            whatsapp_number: data.whatsapp_number,
            business_plan: data.business_plan
        };
        return {
            success: true,
            message: 'Post fetched successfully',
            data: post
        };
    } catch (error) {
        console.error('Unexpected error in fetchSinglePost:', error);
        // Retry logic for network errors
        if (retryCount < maxRetries && error instanceof Error && (error.message.includes('network') || error.message.includes('timeout'))) {
            console.log(`Retrying fetchSinglePost, attempt ${retryCount + 1}/${maxRetries}`);
            await new Promise((resolve)=>setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
            return fetchSinglePost(postId, retryCount + 1);
        }
        return {
            success: false,
            message: 'An unexpected error occurred',
            error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
        };
    }
}
}}),
"[project]/components/post/SinglePostView.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/post/SinglePostView.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/post/SinglePostView.tsx <module evaluation>", "default");
}}),
"[project]/components/post/SinglePostView.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/post/SinglePostView.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/post/SinglePostView.tsx", "default");
}}),
"[project]/components/post/SinglePostView.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$SinglePostView$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/components/post/SinglePostView.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$SinglePostView$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/components/post/SinglePostView.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$SinglePostView$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/components/post/BackNavigation.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/post/BackNavigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/post/BackNavigation.tsx <module evaluation>", "default");
}}),
"[project]/components/post/BackNavigation.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/post/BackNavigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/post/BackNavigation.tsx", "default");
}}),
"[project]/components/post/BackNavigation.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$BackNavigation$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/components/post/BackNavigation.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$BackNavigation$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/components/post/BackNavigation.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$BackNavigation$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/components/post/ConditionalPostLayout.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/post/ConditionalPostLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/post/ConditionalPostLayout.tsx <module evaluation>", "default");
}}),
"[project]/components/post/ConditionalPostLayout.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/post/ConditionalPostLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/post/ConditionalPostLayout.tsx", "default");
}}),
"[project]/components/post/ConditionalPostLayout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$ConditionalPostLayout$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/components/post/ConditionalPostLayout.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$ConditionalPostLayout$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/components/post/ConditionalPostLayout.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$ConditionalPostLayout$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/post/[postId]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SinglePostPage),
    "dynamic": (()=>dynamic),
    "generateMetadata": (()=>generateMetadata),
    "generateStaticParams": (()=>generateStaticParams),
    "revalidate": (()=>revalidate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$fetchSinglePost$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/fetchSinglePost.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$SinglePostView$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/post/SinglePostView.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$feed$2f$shared$2f$PostCardSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/feed/shared/PostCardSkeleton.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$BackNavigation$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/post/BackNavigation.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$ConditionalPostLayout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/post/ConditionalPostLayout.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
async function SinglePostPage({ params }) {
    const { postId } = await params;
    // Validate postId parameter
    if (!postId || typeof postId !== 'string') {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Fetch post data using server action
    const postResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$fetchSinglePost$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchSinglePost"])(postId);
    // Handle post not found or error
    if (!postResult.success || !postResult.data) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$ConditionalPostLayout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$BackNavigation$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/post/[postId]/page.tsx",
                lineNumber: 36,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspense"], {
                fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$feed$2f$shared$2f$PostCardSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                    showImage: true,
                    showProducts: true
                }, void 0, false, {
                    fileName: "[project]/app/post/[postId]/page.tsx",
                    lineNumber: 37,
                    columnNumber: 27
                }, void 0),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$post$2f$SinglePostView$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                    post: postResult.data
                }, void 0, false, {
                    fileName: "[project]/app/post/[postId]/page.tsx",
                    lineNumber: 38,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/post/[postId]/page.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/post/[postId]/page.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
}
async function generateMetadata({ params }) {
    const { postId } = await params;
    const siteUrl = ("TURBOPACK compile-time value", "http://localhost:3000") || "https://dukancard.in";
    const pageUrl = `${siteUrl}/post/${postId}`;
    // Fetch post data for metadata
    const postResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$fetchSinglePost$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchSinglePost"])(postId);
    if (!postResult.success || !postResult.data) {
        return {
            title: "Post Not Found - Dukancard",
            description: "The requested post could not be found."
        };
    }
    const post = postResult.data;
    const title = `${post.author_name || 'Post'} - Dukancard`;
    const description = post.content.length > 160 ? `${post.content.substring(0, 157)}...` : post.content;
    return {
        title,
        description,
        openGraph: {
            title,
            description,
            url: pageUrl,
            type: 'article',
            images: post.image_url ? [
                {
                    url: post.image_url,
                    width: 1200,
                    height: 630,
                    alt: `Post by ${post.author_name}`
                }
            ] : [],
            publishedTime: post.created_at,
            authors: post.author_name ? [
                post.author_name
            ] : []
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            images: post.image_url ? [
                post.image_url
            ] : []
        },
        alternates: {
            canonical: pageUrl
        }
    };
}
const dynamic = 'force-dynamic';
const revalidate = 3600; // Revalidate every hour
const generateStaticParams = async ()=>{
    // Return empty array to enable ISR for all post IDs
    return [];
};
}}),
"[project]/app/post/[postId]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/post/[postId]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__efac62a4._.js.map