import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { Package, Users, Calendar, Clock } from 'lucide-react-native';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface AboutTabProps {
  businessData: BusinessDiscoveryData;
  isDark: boolean;
}

export default function AboutTab({ businessData, isDark }: AboutTabProps) {
  const styles = createPublicCardViewStyles(isDark);
  const iconColor = isDark ? '#9CA3AF' : '#6B7280';

  const formatBusinessHours = () => {
    if (!businessData.business_hours || typeof businessData.business_hours !== 'object') {
      return [];
    }

    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    return days.map((day, index) => {
      const dayData = businessData.business_hours[day];
      const dayName = dayNames[index];

      if (!dayData || !dayData.isOpen) {
        return { day: dayName, hours: 'Closed' };
      }

      const formatTime = (time) => {
        const [hour, minute] = time.split(':');
        const hourNum = parseInt(hour);
        const period = hourNum >= 12 ? 'PM' : 'AM';
        const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;
        return `${displayHour}:${minute} ${period}`;
      };

      const openTime = formatTime(dayData.openTime);
      const closeTime = formatTime(dayData.closeTime);
      return { day: dayName, hours: `${openTime} - ${closeTime}` };
    });
  };

  const businessHoursData = formatBusinessHours();

  return (
    <ScrollView style={styles.section} showsVerticalScrollIndicator={false}>
      {/* About Bio */}
      {businessData.about_bio && (
        <View style={styles.aboutSection}>
          <Text style={styles.aboutText}>{businessData.about_bio}</Text>
        </View>
      )}

      {/* Business Information Section */}
      <View style={styles.categorySection}>
        <Text style={styles.categoryTitle}>Business Information</Text>
        <View style={styles.aboutTableContainer}>
          {businessData.business_name && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Package color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Business Name</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.business_name}</Text>
            </View>
          )}

          {businessData.member_name && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Users color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Owner Name</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.member_name}</Text>
            </View>
          )}

          {businessData.business_category && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Package color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Category</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.business_category}</Text>
            </View>
          )}

          {businessData.established_year && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Calendar color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Established</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.established_year}</Text>
            </View>
          )}

          <View style={styles.aboutTableRow}>
            <View style={styles.aboutTableLabel}>
              <Clock color={iconColor} size={16} />
              <Text style={styles.aboutTableLabelText}>Business Status</Text>
            </View>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>Open</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Business Hours Section */}
      {businessHoursData.length > 0 && (
        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Business Hours</Text>
          <View style={styles.aboutTableContainer}>
            {businessHoursData.map((item, index) => (
              <View key={index} style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Clock color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>{item.day}</Text>
                </View>
                <Text style={styles.aboutTableValue}>{item.hours}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Address Section */}
      {(businessData.address_line || businessData.locality || businessData.city) && (
        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Address</Text>
          <View style={styles.aboutTableContainer}>
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Package color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Location</Text>
              </View>
              <Text style={styles.aboutTableValue}>
                {[
                  businessData.address_line,
                  businessData.locality,
                  businessData.city,
                  businessData.state,
                  businessData.pincode,
                ].filter(Boolean).join(', ')}
              </Text>
            </View>
          </View>
        </View>
      )}
    </ScrollView>
  );
}
