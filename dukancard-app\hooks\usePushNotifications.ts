import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import * as Notifications from 'expo-notifications';
import { useAuth } from '@/contexts/AuthContext';
import { useCustomerProfile, useBusinessProfile } from '@/contexts/UserDataContext';
import PushNotificationService from '@/lib/services/pushNotificationService';
import PushNotificationQueueProcessor from '@/lib/services/pushNotificationQueue';
import { useRouter } from 'expo-router';

export function usePushNotifications() {
  const { user, profileStatus } = useAuth();
  const { customerProfile } = useCustomerProfile();
  const { businessProfile } = useBusinessProfile();

  // Get display name based on user type
  const displayName = businessProfile?.business_name || customerProfile?.name || 'User';
  const router = useRouter();
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    let isMounted = true;

    const initializePushNotifications = async () => {
      if (!user || !isMounted) return;

      try {
        // Initialize push notification service
        const pushService = PushNotificationService.getInstance();
        const token = await pushService.initialize();

        if (token && isMounted) {
          // Determine user type
          const userType = profileStatus?.roleStatus?.hasBusinessProfile ? 'business' : 'customer';
          
          // Store push token in database
          await pushService.storePushToken(user.id, userType);

          // Start queue processor for business users
          if (userType === 'business') {
            PushNotificationQueueProcessor.startProcessing();
          }

          // Push notifications initialized successfully
        }
      } catch (error) {
        console.error('Failed to initialize push notifications:', error);
      }
    };

    // Initialize when user is available
    initializePushNotifications();

    // Set up notification listeners
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      // Handle notification received while app is in foreground
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      handleNotificationResponse(response);
    });

    // Handle app state changes
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        if (user && profileStatus?.roleStatus?.hasBusinessProfile) {
          // Trigger queue processing when business user opens app
          PushNotificationQueueProcessor.triggerProcessing();
        }
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      isMounted = false;
      
      // Clean up listeners
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
      
      subscription?.remove();
      
      // Stop queue processor
      PushNotificationQueueProcessor.stopProcessing();
    };
  }, [user, profileStatus]);

  /**
   * Handle notification tap/response
   */
  const handleNotificationResponse = (response: Notifications.NotificationResponse) => {
    const data = response.notification.request.content.data;
    
    if (data && typeof data === 'object') {
      const { type, businessId, userId, businessSlug, postId } = data;

      switch (type) {
        case 'like':
        case 'subscribe':
        case 'rating':
        case 'visit':
          // Navigate to business dashboard or specific screen
          if (businessSlug) {
            router.push(`/business/${businessSlug}`);
          } else {
            router.push('/(dashboard)/business/');
          }
          break;
        
        case 'post_interaction':
          if (postId) {
            router.push(`/post/${postId}`);
          }
          break;
        
        default:
          // Default navigation
          router.push('/(dashboard)/business/');
          break;
      }
    }
  };

  /**
   * Request notification permissions
   */
  const requestPermissions = async (): Promise<boolean> => {
    try {
      const pushService = PushNotificationService.getInstance();
      return await pushService.areNotificationsEnabled();
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  };

  /**
   * Open notification settings
   */
  const openSettings = async (): Promise<void> => {
    try {
      const pushService = PushNotificationService.getInstance();
      await pushService.openNotificationSettings();
    } catch (error) {
      console.error('Error opening notification settings:', error);
    }
  };

  /**
   * Send a test notification (for development)
   */
  const sendTestNotification = async (): Promise<void> => {
    if (!user) return;

    try {
      const pushService = PushNotificationService.getInstance();
      await pushService.scheduleLocalNotification(
        {
          title: 'Test Notification',
          body: 'This is a test notification from DukanCard',
          data: { type: 'test' },
        },
        { seconds: 1 }
      );
    } catch (error) {
      console.error('Error sending test notification:', error);
    }
  };

  /**
   * Get queue statistics (for business users)
   */
  const getQueueStats = async () => {
    if (!profileStatus?.roleStatus?.hasBusinessProfile) {
      return null;
    }

    try {
      return await PushNotificationQueueProcessor.getQueueStats();
    } catch (error) {
      console.error('Error getting queue stats:', error);
      return null;
    }
  };

  return {
    requestPermissions,
    openSettings,
    sendTestNotification,
    getQueueStats,
  };
}
