import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { Star } from 'lucide-react-native';
import { BusinessReview, BusinessCardData } from '../../lib/services/businessCardDataService';
import { LoadingSpinner } from '../shared/ui/LoadingSpinner';
import { formatIndianNumberShort } from '@/lib/utils';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface ReviewsTabProps {
  reviews: BusinessReview[];
  reviewStats: BusinessCardData['reviewStats'] | null;
  isDark: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

export default function ReviewsTab({
  reviews,
  reviewStats,
  isDark,
  loadingMore,
  hasMore,
  onLoadMore,
}: ReviewsTabProps) {
  const styles = createPublicCardViewStyles(isDark);

  return (
    <View style={styles.section}>
      {reviewStats && (
        <View style={styles.reviewStatsContainer}>
          <View style={styles.ratingOverview}>
            <Text style={styles.averageRating}>{reviewStats.averageRating.toFixed(1)}</Text>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  color={star <= reviewStats.averageRating ? '#f39c12' : '#ddd'}
                  fill={star <= reviewStats.averageRating ? '#f39c12' : 'transparent'}
                />
              ))}
            </View>
            <Text style={styles.totalReviews}>({formatIndianNumberShort(reviewStats.totalReviews)} reviews)</Text>
          </View>
        </View>
      )}

      {reviews && reviews.length > 0 ? (
        <FlatList
          data={reviews}
          keyExtractor={(item) => item.id}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          onEndReached={onLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={() =>
            loadingMore ? (
              <View style={{ padding: 20, alignItems: 'center' }}>
                <LoadingSpinner size="small" />
              </View>
            ) : null
          }
          renderItem={({ item }) => (
            <View style={styles.reviewCard}>
              <View style={styles.reviewHeader}>
                <View style={styles.reviewerInfo}>
                  <View style={styles.reviewerAvatar}>
                    <Text style={styles.reviewerInitial}>
                      {item.customer_name?.charAt(0).toUpperCase() || 'U'}
                    </Text>
                  </View>
                  <View>
                    <Text style={styles.reviewerName}>
                      {item.customer_name || 'Anonymous'}
                    </Text>
                    <View style={styles.reviewStars}>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          size={12}
                          color={star <= item.rating ? '#f39c12' : '#ddd'}
                          fill={star <= item.rating ? '#f39c12' : 'transparent'}
                        />
                      ))}
                    </View>
                  </View>
                </View>
                <Text style={styles.reviewDate}>
                  {new Date(item.created_at).toLocaleDateString()}
                </Text>
              </View>
              {item.review_text && (
                <Text style={styles.reviewText}>{item.review_text}</Text>
              )}
            </View>
          )}
        />
      ) : (
        <Text style={styles.emptyText}>No reviews available</Text>
      )}
    </View>
  );
}
