import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Modal,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Text,
} from 'react-native';
import {
  PanGestureHandler,
  PinchGestureHandler,
  State,
} from 'react-native-gesture-handler';
import { X, ChevronLeft, ChevronRight } from 'lucide-react-native';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { useColorScheme } from '@/hooks/useColorScheme';

interface FullScreenImageViewerProps {
  visible: boolean;
  images: Array<{ id: string; url: string; caption?: string }>;
  initialIndex: number;
  onClose: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function FullScreenImageViewer({
  visible,
  images,
  initialIndex,
  onClose,
}: FullScreenImageViewerProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);

  // Refs for gesture handlers
  const panRef = useRef();
  const pinchRef = useRef();

  // Reset animation values when image changes
  const resetAnimation = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    opacity.value = withSpring(1);
  };

  // Reset current index when modal opens with new initial index
  useEffect(() => {
    if (visible) {
      setCurrentIndex(initialIndex);
      resetAnimation();
    }
  }, [visible, initialIndex]);

  // Navigate to previous image
  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      resetAnimation();
    }
  };

  // Navigate to next image
  const goToNext = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1);
      resetAnimation();
    }
  };

  // Pinch gesture handler for zoom
  const pinchGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startScale = scale.value;
    },
    onActive: (event, context) => {
      scale.value = Math.max(0.5, Math.min(3, context.startScale * event.scale));
    },
    onEnd: () => {
      if (scale.value < 1) {
        scale.value = withSpring(1);
      } else if (scale.value > 2.5) {
        scale.value = withSpring(2.5);
      }
    },
  });

  // Pan gesture handler for drag
  const panGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startX = translateX.value;
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      if (scale.value > 1) {
        // If zoomed in, allow panning
        translateX.value = context.startX + event.translationX;
        translateY.value = context.startY + event.translationY;
      } else {
        // If not zoomed, allow horizontal swipe for navigation and vertical for close
        translateX.value = event.translationX;
        translateY.value = event.translationY;
        opacity.value = Math.max(0.3, 1 - Math.abs(event.translationY) / 300);
      }
    },
    onEnd: (event) => {
      if (scale.value <= 1) {
        // Check horizontal swipe for navigation (prioritize horizontal over vertical)
        if (Math.abs(event.translationX) > 50 && Math.abs(event.translationX) > Math.abs(event.translationY)) {
          if (event.translationX > 0 && currentIndex > 0) {
            // Swipe right - go to previous
            runOnJS(goToPrevious)();
          } else if (event.translationX < 0 && currentIndex < images.length - 1) {
            // Swipe left - go to next
            runOnJS(goToNext)();
          }
          translateX.value = withSpring(0);
          translateY.value = withSpring(0);
          opacity.value = withSpring(1);
        }
        // Check if should close modal (vertical swipe)
        else if (Math.abs(event.translationY) > 100 || Math.abs(event.velocityY) > 500) {
          runOnJS(onClose)();
        } else {
          translateX.value = withSpring(0);
          translateY.value = withSpring(0);
          opacity.value = withSpring(1);
        }
      } else {
        // Constrain pan within bounds when zoomed
        const maxTranslateX = (screenWidth * (scale.value - 1)) / 2;
        const maxTranslateY = (screenHeight * (scale.value - 1)) / 2;

        translateX.value = withSpring(
          Math.max(-maxTranslateX, Math.min(maxTranslateX, translateX.value))
        );
        translateY.value = withSpring(
          Math.max(-maxTranslateY, Math.min(maxTranslateY, translateY.value))
        );
      }
    },
  });

  // Animated style for the image
  const animatedImageStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  });

  // Animated style for the modal background
  const animatedBackgroundStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  // Handle modal close
  const handleClose = () => {
    resetAnimation();
    onClose();
  };

  if (!visible || !images.length) {
    return null;
  }

  const currentImage = images[currentIndex];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <StatusBar hidden />
      <Animated.View style={[styles.container, animatedBackgroundStyle]}>
        {/* Close button */}
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <X size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Navigation buttons */}
        {images.length > 1 && (
          <>
            {currentIndex > 0 && (
              <TouchableOpacity style={styles.prevButton} onPress={goToPrevious}>
                <ChevronLeft size={32} color="#FFFFFF" />
              </TouchableOpacity>
            )}
            {currentIndex < images.length - 1 && (
              <TouchableOpacity style={styles.nextButton} onPress={goToNext}>
                <ChevronRight size={32} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </>
        )}

        {/* Image container */}
        <View style={styles.imageContainer}>
          <PinchGestureHandler ref={pinchRef} onGestureEvent={pinchGestureHandler}>
            <Animated.View style={styles.gestureContainer}>
              <PanGestureHandler
                ref={panRef}
                onGestureEvent={panGestureHandler}
                simultaneousHandlers={pinchRef}
              >
                <Animated.View style={styles.gestureContainer}>
                  <Animated.Image
                    source={{ uri: currentImage.url }}
                    style={[styles.image, animatedImageStyle]}
                    resizeMode="contain"
                  />
                </Animated.View>
              </PanGestureHandler>
            </Animated.View>
          </PinchGestureHandler>
        </View>

        {/* Image counter */}
        {images.length > 1 && (
          <View style={styles.counter}>
            <Text style={styles.counterText}>
              {currentIndex + 1} / {images.length}
            </Text>
          </View>
        )}
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  prevButton: {
    position: 'absolute',
    left: 20,
    top: '50%',
    marginTop: -22,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButton: {
    position: 'absolute',
    right: 20,
    top: '50%',
    marginTop: -22,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gestureContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: screenWidth,
    height: screenHeight,
  },
  counter: {
    position: 'absolute',
    bottom: 50,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  counterText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});
