import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface PublicCardHeaderProps {
  businessData: BusinessDiscoveryData;
  isDark: boolean;
  onClose?: () => void;
}

export default function PublicCardHeader({ businessData, isDark, onClose }: PublicCardHeaderProps) {
  const [imageError, setImageError] = useState(false);
  const styles = createPublicCardViewStyles(isDark);
  const themeColor = businessData.theme_color || '#D4AF37';

  return (
    <View style={[styles.header, { backgroundColor: themeColor }]}>
      {/* Back Button with Circular Background */}
      <TouchableOpacity
        style={styles.backButtonContainer}
        onPress={onClose}
        activeOpacity={0.7}
      >
        <ArrowLeft color="#fff" size={24} />
      </TouchableOpacity>

      <View style={styles.logoContainer}>
        {businessData.logo_url && !imageError ? (
          <Image
            source={{ uri: businessData.logo_url }}
            style={styles.logo}
            onError={() => setImageError(true)}
          />
        ) : (
          <View style={[styles.logoPlaceholder, { backgroundColor: 'rgba(255,255,255,0.2)' }]}>
            <Text style={styles.logoText}>
              {businessData.business_name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.headerInfo}>
        <Text style={styles.businessName}>{businessData.business_name}</Text>
      </View>
    </View>
  );
}
