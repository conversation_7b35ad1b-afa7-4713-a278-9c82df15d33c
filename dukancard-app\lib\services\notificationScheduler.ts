import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';
import PushNotificationService from './pushNotificationService';

interface ScheduledNotification {
  id: string;
  type: 'engagement' | 'business_tip' | 'analytics_reminder' | 'post_reminder';
  title: string;
  body: string;
  scheduledFor: Date;
  userId: string;
  userType: 'customer' | 'business';
  data?: any;
}

interface NotificationFrequency {
  lastEngagementNotification: number;
  lastBusinessTip: number;
  lastAnalyticsReminder: number;
  lastPostReminder: number;
  totalNotificationsThisWeek: number;
  weekStartDate: string;
}

class NotificationScheduler {
  private static instance: NotificationScheduler;
  private readonly STORAGE_KEY = 'notification_frequency';
  private readonly MAX_NOTIFICATIONS_PER_WEEK = 3; // Based on research: 1 push/week = 10% disable
  private readonly MIN_HOURS_BETWEEN_NOTIFICATIONS = 24; // Minimum 24 hours between notifications

  static getInstance(): NotificationScheduler {
    if (!NotificationScheduler.instance) {
      NotificationScheduler.instance = new NotificationScheduler();
    }
    return NotificationScheduler.instance;
  }

  /**
   * Initialize notification scheduling for a user
   */
  async initializeForUser(userId: string, userType: 'customer' | 'business'): Promise<void> {
    try {
      // Clear any existing scheduled notifications for this user
      await this.clearUserNotifications(userId);

      // Schedule appropriate notifications based on user type
      if (userType === 'business') {
        await this.scheduleBusinessNotifications(userId);
      } else {
        await this.scheduleCustomerNotifications(userId);
      }
    } catch (error) {
      console.error('Error initializing notifications for user:', error);
    }
  }

  /**
   * Schedule notifications for business users
   */
  private async scheduleBusinessNotifications(userId: string): Promise<void> {
    const frequency = await this.getNotificationFrequency(userId);
    
    if (!this.canScheduleNotification(frequency)) {
      return; // Already reached weekly limit
    }

    // Schedule analytics reminder (Tuesday at 12 PM - best day/time based on research)
    if (this.shouldScheduleAnalyticsReminder(frequency)) {
      await this.scheduleAnalyticsReminder(userId);
    }

    // Schedule post reminder (if no posts in last 3 days)
    if (await this.shouldSchedulePostReminder(userId)) {
      await this.schedulePostReminder(userId);
    }

    // Schedule business tip (weekly)
    if (this.shouldScheduleBusinessTip(frequency)) {
      await this.scheduleBusinessTip(userId);
    }
  }

  /**
   * Schedule notifications for customer users
   */
  private async scheduleCustomerNotifications(userId: string): Promise<void> {
    const frequency = await this.getNotificationFrequency(userId);
    
    if (!this.canScheduleNotification(frequency)) {
      return; // Already reached weekly limit
    }

    // Schedule engagement reminder (if no activity in last 5 days)
    if (await this.shouldScheduleEngagementReminder(userId)) {
      await this.scheduleEngagementReminder(userId);
    }

    // Schedule local discovery reminder (weekly)
    if (this.shouldScheduleDiscoveryReminder(frequency)) {
      await this.scheduleDiscoveryReminder(userId);
    }
  }

  /**
   * Schedule analytics reminder for business users
   */
  private async scheduleAnalyticsReminder(userId: string): Promise<void> {
    const nextTuesday = this.getNextTuesday();
    nextTuesday.setHours(12, 0, 0, 0); // 12 PM

    const notificationId = await PushNotificationService.scheduleLocalNotification(
      {
        title: '📊 Weekly Analytics Ready!',
        body: 'Check your business performance and see how customers are engaging with your profile.',
        data: {
          type: 'analytics_reminder',
          url: '/(dashboard)/business/activities',
        },
      },
      { date: nextTuesday }
    );

    if (notificationId) {
      await this.updateNotificationFrequency(userId, 'lastAnalyticsReminder');
    }
  }

  /**
   * Schedule post reminder for business users
   */
  private async schedulePostReminder(userId: string): Promise<void> {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0); // 10 AM

    const notificationId = await PushNotificationService.scheduleLocalNotification(
      {
        title: '✨ Time to Share Something New!',
        body: 'Keep your customers engaged by sharing what\'s happening at your business today.',
        data: {
          type: 'post_reminder',
          url: '/(dashboard)/business',
        },
      },
      { date: tomorrow }
    );

    if (notificationId) {
      await this.updateNotificationFrequency(userId, 'lastPostReminder');
    }
  }

  /**
   * Schedule business tip notification
   */
  private async scheduleBusinessTip(userId: string): Promise<void> {
    const tips = [
      {
        title: '💡 Business Tip: Update Your Hours',
        body: 'Keep your business hours updated so customers know when you\'re open!',
      },
      {
        title: '📸 Business Tip: Add Photos',
        body: 'Businesses with photos get 3x more customer engagement!',
      },
      {
        title: '🌟 Business Tip: Respond to Reviews',
        body: 'Responding to customer reviews builds trust and improves your rating.',
      },
      {
        title: '📱 Business Tip: Share Your QR Code',
        body: 'Print your QR code and display it at your business for easy customer access.',
      },
    ];

    const randomTip = tips[Math.floor(Math.random() * tips.length)];
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    nextWeek.setHours(9, 0, 0, 0); // 9 AM

    const notificationId = await PushNotificationService.scheduleLocalNotification(
      {
        title: randomTip.title,
        body: randomTip.body,
        data: {
          type: 'business_tip',
          url: '/(dashboard)/business/profile',
        },
      },
      { date: nextWeek }
    );

    if (notificationId) {
      await this.updateNotificationFrequency(userId, 'lastBusinessTip');
    }
  }

  /**
   * Schedule engagement reminder for customer users
   */
  private async scheduleEngagementReminder(userId: string): Promise<void> {
    const dayAfterTomorrow = new Date();
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
    dayAfterTomorrow.setHours(19, 0, 0, 0); // 7 PM (evening wind-down time)

    const notificationId = await PushNotificationService.scheduleLocalNotification(
      {
        title: '🏪 Discover Local Businesses',
        body: 'See what\'s new in your area and support local businesses around you!',
        data: {
          type: 'engagement',
          url: '/(dashboard)/customer',
        },
      },
      { date: dayAfterTomorrow }
    );

    if (notificationId) {
      await this.updateNotificationFrequency(userId, 'lastEngagementNotification');
    }
  }

  /**
   * Schedule discovery reminder for customer users
   */
  private async scheduleDiscoveryReminder(userId: string): Promise<void> {
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    nextWeek.setHours(18, 30, 0, 0); // 6:30 PM

    const notificationId = await PushNotificationService.scheduleLocalNotification(
      {
        title: '🔍 Explore Your Neighborhood',
        body: 'Find new restaurants, shops, and services in your local area.',
        data: {
          type: 'engagement',
          url: '/(dashboard)/customer/discover',
        },
      },
      { date: nextWeek }
    );

    if (notificationId) {
      await this.updateNotificationFrequency(userId, 'lastEngagementNotification');
    }
  }

  /**
   * Check if user should receive analytics reminder
   */
  private shouldScheduleAnalyticsReminder(frequency: NotificationFrequency): boolean {
    const lastReminder = frequency.lastAnalyticsReminder;
    const weekInMs = 7 * 24 * 60 * 60 * 1000;
    return Date.now() - lastReminder > weekInMs;
  }

  /**
   * Check if user should receive post reminder
   */
  private async shouldSchedulePostReminder(userId: string): Promise<boolean> {
    try {
      // Check if user has posted in the last 3 days
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

      const { data, error } = await supabase
        .from('business_posts')
        .select('id')
        .eq('business_id', userId)
        .gte('created_at', threeDaysAgo.toISOString())
        .limit(1);

      if (error) {
        console.error('Error checking recent posts:', error);
        return false;
      }

      return !data || data.length === 0; // No recent posts
    } catch (error) {
      console.error('Error checking post reminder:', error);
      return false;
    }
  }

  /**
   * Check if user should receive business tip
   */
  private shouldScheduleBusinessTip(frequency: NotificationFrequency): boolean {
    const lastTip = frequency.lastBusinessTip;
    const weekInMs = 7 * 24 * 60 * 60 * 1000;
    return Date.now() - lastTip > weekInMs;
  }

  /**
   * Check if user should receive engagement reminder
   */
  private async shouldScheduleEngagementReminder(userId: string): Promise<boolean> {
    try {
      // Check if user has been active in the last 5 days
      const fiveDaysAgo = new Date();
      fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

      // Check for any recent activity (likes, subscriptions, etc.)
      const { data, error } = await supabase
        .from('business_activities')
        .select('id')
        .eq('user_id', userId)
        .gte('created_at', fiveDaysAgo.toISOString())
        .limit(1);

      if (error) {
        console.error('Error checking recent activity:', error);
        return false;
      }

      return !data || data.length === 0; // No recent activity
    } catch (error) {
      console.error('Error checking engagement reminder:', error);
      return false;
    }
  }

  /**
   * Check if discovery reminder should be scheduled
   */
  private shouldScheduleDiscoveryReminder(frequency: NotificationFrequency): boolean {
    const lastReminder = frequency.lastEngagementNotification;
    const weekInMs = 7 * 24 * 60 * 60 * 1000;
    return Date.now() - lastReminder > weekInMs;
  }

  /**
   * Get next Tuesday date
   */
  private getNextTuesday(): Date {
    const today = new Date();
    const daysUntilTuesday = (2 - today.getDay() + 7) % 7 || 7; // Tuesday is day 2
    const nextTuesday = new Date(today);
    nextTuesday.setDate(today.getDate() + daysUntilTuesday);
    return nextTuesday;
  }

  /**
   * Check if user can receive more notifications this week
   */
  private canScheduleNotification(frequency: NotificationFrequency): boolean {
    // Reset weekly counter if it's a new week
    const currentWeekStart = this.getWeekStartDate();
    if (frequency.weekStartDate !== currentWeekStart) {
      frequency.totalNotificationsThisWeek = 0;
      frequency.weekStartDate = currentWeekStart;
    }

    return frequency.totalNotificationsThisWeek < this.MAX_NOTIFICATIONS_PER_WEEK;
  }

  /**
   * Get notification frequency data for user
   */
  private async getNotificationFrequency(userId: string): Promise<NotificationFrequency> {
    try {
      const stored = await AsyncStorage.getItem(`${this.STORAGE_KEY}_${userId}`);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error getting notification frequency:', error);
    }

    // Return default frequency data
    return {
      lastEngagementNotification: 0,
      lastBusinessTip: 0,
      lastAnalyticsReminder: 0,
      lastPostReminder: 0,
      totalNotificationsThisWeek: 0,
      weekStartDate: this.getWeekStartDate(),
    };
  }

  /**
   * Update notification frequency data
   */
  private async updateNotificationFrequency(
    userId: string,
    type: keyof Omit<NotificationFrequency, 'totalNotificationsThisWeek' | 'weekStartDate'>
  ): Promise<void> {
    try {
      const frequency = await this.getNotificationFrequency(userId);
      frequency[type] = Date.now();
      frequency.totalNotificationsThisWeek += 1;
      
      await AsyncStorage.setItem(
        `${this.STORAGE_KEY}_${userId}`,
        JSON.stringify(frequency)
      );
    } catch (error) {
      console.error('Error updating notification frequency:', error);
    }
  }

  /**
   * Get the start date of current week (Monday)
   */
  private getWeekStartDate(): string {
    const today = new Date();
    const monday = new Date(today);
    monday.setDate(today.getDate() - ((today.getDay() + 6) % 7));
    return monday.toISOString().split('T')[0];
  }

  /**
   * Clear all scheduled notifications for a user
   */
  async clearUserNotifications(userId: string): Promise<void> {
    try {
      // Cancel all scheduled notifications
      await PushNotificationService.cancelAllNotifications();
      
      // Reset frequency data
      await AsyncStorage.removeItem(`${this.STORAGE_KEY}_${userId}`);
    } catch (error) {
      console.error('Error clearing user notifications:', error);
    }
  }

  /**
   * Pause notifications for a user (when they're actively using the app)
   */
  async pauseNotifications(userId: string): Promise<void> {
    try {
      await PushNotificationService.cancelAllNotifications();
    } catch (error) {
      console.error('Error pausing notifications:', error);
    }
  }

  /**
   * Resume notifications for a user (when they close the app)
   */
  async resumeNotifications(userId: string, userType: 'customer' | 'business'): Promise<void> {
    try {
      await this.initializeForUser(userId, userType);
    } catch (error) {
      console.error('Error resuming notifications:', error);
    }
  }
}

export default NotificationScheduler.getInstance();
