import { useState, useEffect } from 'react';
import {
  fetchBusinessCardData,
  BusinessCardData,
  BusinessProduct,
  BusinessReview,
  fetchBusinessProductsPaginated,
  fetchBusinessReviewsPaginated
} from '../lib/services/businessCardDataService';

export const useBusinessCardData = (businessId: string, businessSlug: string) => {
  const [cardData, setCardData] = useState<BusinessCardData | null>(null);
  const [loadingCardData, setLoadingCardData] = useState(true);

  useEffect(() => {
    const loadCardData = async () => {
      if (!businessId || !businessSlug) return;

      try {
        setLoadingCardData(true);
        const result = await fetchBusinessCardData(businessId, businessSlug);

        if (result.success && result.data) {
          setCardData(result.data);
        } else {
          console.warn('Failed to load card data:', result.error);
        }
      } catch (error) {
        console.error('Error loading card data:', error);
      } finally {
        setLoadingCardData(false);
      }
    };

    loadCardData();
  }, [businessId, businessSlug]);

  return { cardData, loadingCardData };
};

export const useProductsPagination = (businessId: string, initialProducts: BusinessProduct[] = []) => {
  const [allProducts, setAllProducts] = useState<BusinessProduct[]>(initialProducts);
  const [productsPage, setProductsPage] = useState(1);
  const [loadingMoreProducts, setLoadingMoreProducts] = useState(false);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);

  // Initialize pagination data
  useEffect(() => {
    if (initialProducts.length > 0) {
      setAllProducts(initialProducts);
    }
  }, [initialProducts]);

  // Load more products
  const loadMoreProducts = async () => {
    if (loadingMoreProducts || !hasMoreProducts) return;

    setLoadingMoreProducts(true);
    try {
      const result = await fetchBusinessProductsPaginated(
        businessId,
        productsPage + 1,
        6
      );

      if (result.success && result.data) {
        setAllProducts(prev => [...prev, ...result.data!]);
        setProductsPage(prev => prev + 1);
        setHasMoreProducts(result.hasMore || false);
      }
    } catch (error) {
      console.error('Error loading more products:', error);
    } finally {
      setLoadingMoreProducts(false);
    }
  };

  return {
    allProducts,
    loadingMoreProducts,
    hasMoreProducts,
    loadMoreProducts,
  };
};

export const useReviewsPagination = (businessId: string, initialReviews: BusinessReview[] = []) => {
  const [allReviews, setAllReviews] = useState<BusinessReview[]>(initialReviews);
  const [reviewsPage, setReviewsPage] = useState(1);
  const [loadingMoreReviews, setLoadingMoreReviews] = useState(false);
  const [hasMoreReviews, setHasMoreReviews] = useState(true);

  // Initialize pagination data
  useEffect(() => {
    if (initialReviews.length > 0) {
      setAllReviews(initialReviews);
    }
  }, [initialReviews]);

  // Load more reviews
  const loadMoreReviews = async () => {
    if (loadingMoreReviews || !hasMoreReviews) return;

    setLoadingMoreReviews(true);
    try {
      const result = await fetchBusinessReviewsPaginated(
        businessId,
        reviewsPage + 1,
        5
      );

      if (result.success && result.data) {
        setAllReviews(prev => [...prev, ...result.data!]);
        setReviewsPage(prev => prev + 1);
        setHasMoreReviews(result.hasMore || false);
      }
    } catch (error) {
      console.error('Error loading more reviews:', error);
    } finally {
      setLoadingMoreReviews(false);
    }
  };

  return {
    allReviews,
    loadingMoreReviews,
    hasMoreReviews,
    loadMoreReviews,
  };
};
