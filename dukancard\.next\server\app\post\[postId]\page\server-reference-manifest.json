{"node": {"40e8d26551daec16eb75e419b18aa90c74990473ee": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "00b5b8ef95d3d0ba8e30b0dfcedbc6f2be25e07e63": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "4044c2d63ef50e6aed243c22339f1fbcd25e807b72": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "60bb9b03f42518a37c9be2d8f3b0d6ea01847ba65c": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "40d09a6de0a98d0c3d2475e2b600f5486900620cf8": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "40a788bd0129ed104e004a10b6f5a8408570e96886": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "407ba87de4ca16d5fb7ed1a57c146fcd6b3e9a5ae1": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}}, "edge": {}}