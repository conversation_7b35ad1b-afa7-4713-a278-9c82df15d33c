import { StyleSheet } from 'react-native';
import { ColorSchemeName } from '@/hooks/useColorScheme';

export const createSharedDiscoverStyles = (colorScheme: ColorSchemeName) => {
  const isDark = colorScheme === 'dark';
  
  return StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isDark ? '#000000' : '#FFFFFF',
      paddingHorizontal: 20,
    },
    placeholder: {
      padding: 32,
      alignItems: 'center',
      backgroundColor: isDark ? 'rgba(212, 175, 55, 0.1)' : 'rgba(212, 175, 55, 0.05)',
      borderRadius: 20,
      borderWidth: 2,
      borderColor: 'rgba(212, 175, 55, 0.3)',
      borderStyle: 'dashed',
      margin: 16,
      maxWidth: 350,
    },
    icon: {
      fontSize: 48,
      marginBottom: 16,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: '#D4AF37',
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 20,
      fontWeight: '600',
      color: isDark ? '#FFFFFF' : '#000000',
      textAlign: 'center',
      marginBottom: 16,
    },
    description: {
      fontSize: 16,
      textAlign: 'center',
      color: isDark ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
      marginBottom: 12,
      lineHeight: 24,
    },
    note: {
      fontSize: 14,
      textAlign: 'center',
      color: isDark ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
      fontStyle: 'italic',
      lineHeight: 20,
    },
  });
};
